#!/bin/bash

# QQ机器人启动脚本

echo "🤖 启动QQ机器人..."

# 检查虚拟环境是否存在
if [ ! -d "qq_bot_env" ]; then
    echo "❌ 虚拟环境不存在，请先运行 ./install.sh"
    exit 1
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source qq_bot_env/bin/activate

# 检查配置
echo "🔍 检查配置..."
python3 -c "
from config import BOT_CONFIG
import sys

required_fields = ['appid', 'token', 'secret']
for field in required_fields:
    value = BOT_CONFIG.get(field, '')
    if not value or value.startswith('你的'):
        print(f'❌ {field} 未正确配置')
        sys.exit(1)
    else:
        print(f'✅ {field} 已配置')

print('✅ 配置检查通过！')
"

if [ $? -ne 0 ]; then
    echo "❌ 配置检查失败，请检查config.py"
    exit 1
fi

# 启动机器人
echo "🚀 启动机器人..."
python3 bot.py
