#!/bin/bash

#==============================================================================
# Koishi Docker 快速部署脚本
# 
# 支持自定义端口的快速部署
# 
# 使用方法：
#   ./koishi-quick-deploy.sh                    # 交互式选择端口
#   ./koishi-quick-deploy.sh 8080              # 直接指定端口
#   ./koishi-quick-deploy.sh --port 3000       # 使用参数指定端口
#
#==============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
DEFAULT_PORT=5140
KOISHI_PORT=""

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

# 进度条显示函数
show_progress() {
    local current=$1
    local total=$2
    local message=$3
    local width=40

    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))

    printf "\r${BLUE}[进度] ${message}${NC} ["
    printf "%${filled}s" | tr ' ' '█'
    printf "%${empty}s" | tr ' ' '░'
    printf "] %d%% (%d/%d)" $percentage $current $total

    if [ $current -eq $total ]; then
        echo
    fi
}

# 显示帮助信息
show_help() {
    echo "Koishi Docker 快速部署脚本"
    echo
    echo "使用方法:"
    echo "  $0                    # 交互式选择端口"
    echo "  $0 8080              # 直接指定端口 8080"
    echo "  $0 --port 3000       # 使用参数指定端口 3000"
    echo "  $0 --help            # 显示帮助信息"
    echo
    echo "支持的端口范围: 1024-65535"
    echo
}

# 验证端口
validate_port() {
    local port=$1
    
    # 检查是否为数字
    if ! [[ "$port" =~ ^[0-9]+$ ]]; then
        return 1
    fi
    
    # 检查端口范围
    if [ "$port" -lt 1024 ] || [ "$port" -gt 65535 ]; then
        return 1
    fi
    
    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        log "WARN" "端口 $port 已被占用"
        return 1
    fi
    
    return 0
}

# 解析命令行参数
parse_arguments() {
    case "$1" in
        --help|-h)
            show_help
            exit 0
            ;;
        --port)
            if [ -n "$2" ]; then
                if validate_port "$2"; then
                    KOISHI_PORT="$2"
                    log "SUCCESS" "使用指定端口: $KOISHI_PORT"
                else
                    log "ERROR" "无效端口: $2"
                    exit 1
                fi
            else
                log "ERROR" "--port 参数需要指定端口号"
                exit 1
            fi
            ;;
        "")
            # 无参数，使用交互式选择
            ;;
        *)
            # 直接指定端口
            if validate_port "$1"; then
                KOISHI_PORT="$1"
                log "SUCCESS" "使用指定端口: $KOISHI_PORT"
            else
                log "ERROR" "无效端口: $1"
                show_help
                exit 1
            fi
            ;;
    esac
}

# 交互式选择端口
interactive_port_selection() {
    if [ -n "$KOISHI_PORT" ]; then
        return 0
    fi
    
    echo
    log "INFO" "请选择 Koishi 运行端口："
    echo
    echo "1) 5140 (默认端口)"
    echo "2) 8080 (常用 Web 端口)"
    echo "3) 3000 (Node.js 常用端口)"
    echo "4) 自定义端口"
    echo
    
    while true; do
        read -p "请输入选项 (1-4): " choice
        
        case $choice in
            1)
                KOISHI_PORT=5140
                break
                ;;
            2)
                KOISHI_PORT=8080
                break
                ;;
            3)
                KOISHI_PORT=3000
                break
                ;;
            4)
                while true; do
                    read -p "请输入自定义端口 (1024-65535): " custom_port
                    if validate_port "$custom_port"; then
                        KOISHI_PORT=$custom_port
                        break 2
                    else
                        log "ERROR" "无效的端口号或端口已被占用"
                    fi
                done
                ;;
            *)
                log "ERROR" "无效选项，请输入 1-4"
                ;;
        esac
    done
    
    log "SUCCESS" "已选择端口: $KOISHI_PORT"
}

# 检查 Docker
check_docker() {
    if ! command -v docker >/dev/null 2>&1; then
        log "ERROR" "Docker 未安装，请先运行完整部署脚本: ./koishi-docker-deploy.sh"
        exit 1
    fi

    if ! docker info >/dev/null 2>&1; then
        log "ERROR" "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi

    log "SUCCESS" "Docker 检查通过"
}

# 检测现有 Koishi 容器
detect_existing_containers() {
    local container_name="koishi-bot-$KOISHI_PORT"

    # 检查所有 Koishi 相关容器
    local existing_containers=$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "koishi|$KOISHI_PORT" | grep -v "NAMES" || true)
    local running_containers=$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "koishi|$KOISHI_PORT" | grep -v "NAMES" || true)

    # 检查端口占用
    local port_occupied=""
    if netstat -tuln 2>/dev/null | grep -q ":$KOISHI_PORT "; then
        port_occupied="yes"
    fi

    # 检查特定容器名称
    local target_container_exists=""
    local target_container_running=""
    if docker ps -a -q -f name="$container_name" | grep -q .; then
        target_container_exists="yes"
        if docker ps -q -f name="$container_name" | grep -q .; then
            target_container_running="yes"
        fi
    fi

    # 返回检测结果
    echo "$existing_containers|$running_containers|$port_occupied|$target_container_exists|$target_container_running|$container_name"
}

# 显示现有容器状态
show_container_status() {
    local container_name="$1"

    echo
    echo -e "${BLUE}======== 现有容器状态 ========${NC}"

    # 显示所有 Koishi 相关容器
    local all_containers=$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}" | grep -E "koishi|$KOISHI_PORT")
    if [ -n "$all_containers" ]; then
        echo -e "${YELLOW}所有 Koishi 相关容器：${NC}"
        echo "$all_containers"
    else
        echo -e "${GREEN}没有发现 Koishi 相关容器${NC}"
    fi

    echo

    # 显示端口占用情况
    echo -e "${YELLOW}端口 $KOISHI_PORT 占用情况：${NC}"
    local port_info=$(netstat -tuln 2>/dev/null | grep ":$KOISHI_PORT " || echo "端口未被占用")
    echo "$port_info"

    echo

    # 显示目标容器状态
    if docker ps -a -q -f name="$container_name" | grep -q .; then
        echo -e "${YELLOW}目标容器 ($container_name) 状态：${NC}"
        docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.CreatedAt}}" -f name="$container_name"

        echo
        echo -e "${YELLOW}容器日志 (最后10行)：${NC}"
        docker logs --tail 10 "$container_name" 2>/dev/null || echo "无法获取日志"
    else
        echo -e "${GREEN}目标容器 ($container_name) 不存在${NC}"
    fi

    echo -e "${BLUE}================================${NC}"
    echo
}

# 重启现有容器
restart_existing_container() {
    local container_name="$1"

    log "INFO" "重启现有容器..."
    echo

    # 步骤1: 停止容器
    show_progress 1 4 "停止容器"
    docker stop "$container_name" >/dev/null 2>&1
    log "SUCCESS" "容器已停止"

    # 步骤2: 启动容器
    show_progress 2 4 "启动容器"
    docker start "$container_name" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log "SUCCESS" "容器已启动"
    else
        log "ERROR" "容器启动失败"
        return 1
    fi

    # 步骤3: 等待服务就绪
    show_progress 3 4 "等待服务就绪"
    for i in {1..10}; do
        show_progress $i 10 "等待服务就绪"
        sleep 1
    done
    echo

    # 步骤4: 验证访问
    show_progress 4 4 "验证服务访问"
    local access_success=0
    for i in {1..3}; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:"$KOISHI_PORT" 2>/dev/null | grep -q "200"; then
            access_success=1
            break
        fi
        sleep 2
    done

    echo
    if [ $access_success -eq 1 ]; then
        log "SUCCESS" "容器重启成功，服务正常运行！"
    else
        log "WARN" "容器已重启，但服务可能仍在初始化"
    fi

    return 0
}

# 交互式容器管理
interactive_container_management() {
    local detection_result="$1"
    IFS='|' read -r existing_containers running_containers port_occupied target_exists target_running container_name <<< "$detection_result"

    # 如果没有发现任何相关容器或端口占用，直接返回继续部署
    if [ -z "$existing_containers" ] && [ "$port_occupied" != "yes" ] && [ "$target_exists" != "yes" ]; then
        log "SUCCESS" "没有发现冲突，继续部署..."
        return 0
    fi

    echo
    log "WARN" "检测到现有的 Koishi 容器或端口占用！"

    while true; do
        echo
        echo -e "${YELLOW}请选择操作：${NC}"
        echo "1) 查看现有容器状态和日志"
        echo "2) 重启现有容器 (如果存在目标容器)"
        echo "3) 停止并重新部署新容器"
        echo "4) 取消操作并退出脚本"
        echo
        read -p "请输入选项 (1-4): " choice

        case $choice in
            1)
                show_container_status "$container_name"
                ;;
            2)
                if [ "$target_exists" = "yes" ]; then
                    if restart_existing_container "$container_name"; then
                        # 显示访问信息
                        echo
                        echo -e "${GREEN}🎉 容器重启完成！${NC}"
                        echo
                        echo -e "${BLUE}访问地址：${NC}"
                        echo "  本地访问: http://localhost:$KOISHI_PORT"
                        echo "  外部访问: http://$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP"):$KOISHI_PORT"
                        echo
                        echo -e "${BLUE}管理命令：${NC}"
                        echo "  查看状态: docker ps | grep $container_name"
                        echo "  查看日志: docker logs $container_name"
                        echo "  停止容器: docker stop $container_name"
                        echo "  重启容器: docker restart $container_name"
                        echo
                        exit 0
                    else
                        log "ERROR" "重启失败，请选择其他操作"
                    fi
                else
                    log "ERROR" "目标容器 ($container_name) 不存在，无法重启"
                    log "INFO" "请选择其他操作或重新部署"
                fi
                ;;
            3)
                log "INFO" "将停止现有容器并重新部署..."
                # 停止所有相关容器
                if [ -n "$running_containers" ]; then
                    log "FIX" "停止所有运行中的 Koishi 容器..."
                    docker ps -q --filter "name=koishi" | xargs -r docker stop >/dev/null 2>&1
                    docker ps -a -q --filter "name=koishi" | xargs -r docker rm >/dev/null 2>&1
                fi

                # 如果端口被占用，尝试释放
                if [ "$port_occupied" = "yes" ]; then
                    log "FIX" "尝试释放端口 $KOISHI_PORT..."
                    local pid=$(netstat -tuln 2>/dev/null | grep ":$KOISHI_PORT " | awk '{print $7}' | cut -d'/' -f1 | head -1)
                    if [ -n "$pid" ] && [ "$pid" != "-" ]; then
                        kill -9 "$pid" 2>/dev/null || true
                    fi
                fi

                log "SUCCESS" "清理完成，继续部署..."
                return 0
                ;;
            4)
                log "INFO" "用户取消操作，退出脚本"
                exit 0
                ;;
            *)
                log "ERROR" "无效选项，请输入 1-4"
                ;;
        esac
    done
}

# 快速部署
quick_deploy() {
    local container_name="koishi-bot-$KOISHI_PORT"
    local data_dir="$HOME/koishi-data-$KOISHI_PORT"

    log "INFO" "开始快速部署 Koishi (端口: $KOISHI_PORT)..."
    echo

    # 步骤1: 准备部署环境
    show_progress 1 5 "准备部署环境"
    log "SUCCESS" "部署环境准备完成"

    # 步骤2: 创建数据目录
    show_progress 2 5 "创建数据目录"
    mkdir -p "$data_dir"
    log "SUCCESS" "数据目录已创建: $data_dir"

    # 步骤3: 拉取镜像
    show_progress 3 5 "拉取 Koishi 镜像"

    # 创建临时文件监控拉取进度
    local temp_log=$(mktemp)
    (
        docker pull koishijs/koishi:latest > "$temp_log" 2>&1
        echo $? > "${temp_log}.exit"
    ) &

    local pull_pid=$!
    local progress_step=3

    # 显示拉取进度动画
    while kill -0 $pull_pid 2>/dev/null; do
        show_progress $progress_step 5 "拉取 Koishi 镜像"
        sleep 0.5
    done

    wait $pull_pid
    local exit_code=$(cat "${temp_log}.exit" 2>/dev/null || echo "1")

    if [ "$exit_code" -eq 0 ]; then
        show_progress 3 5 "拉取 Koishi 镜像"
        echo
        log "SUCCESS" "镜像拉取完成"
    else
        echo
        log "ERROR" "镜像拉取失败"
        cat "$temp_log"
        rm -f "$temp_log" "${temp_log}.exit"
        return 1
    fi

    rm -f "$temp_log" "${temp_log}.exit"

    # 步骤4: 启动容器
    show_progress 4 5 "启动 Koishi 容器"
    docker run -d \
        --name "$container_name" \
        --restart unless-stopped \
        -p "$KOISHI_PORT:5140" \
        -v "$data_dir:/koishi/data" \
        -e TZ=Asia/Shanghai \
        koishijs/koishi:latest >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        show_progress 4 5 "启动 Koishi 容器"
        echo
        log "SUCCESS" "容器启动成功！"

        # 配置防火墙
        if command -v ufw >/dev/null 2>&1; then
            sudo ufw allow "$KOISHI_PORT" >/dev/null 2>&1
            log "SUCCESS" "防火墙规则已配置"
        fi

        # 步骤5: 等待服务启动
        show_progress 5 5 "等待服务启动"
        for i in {1..10}; do
            show_progress $i 10 "等待服务启动"
            sleep 1
        done
        echo
        
        # 显示访问信息
        echo
        echo -e "${GREEN}🎉 Koishi 部署完成！${NC}"
        echo
        echo -e "${BLUE}访问地址：${NC}"
        echo "  本地访问: http://localhost:$KOISHI_PORT"
        echo "  外部访问: http://$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP"):$KOISHI_PORT"
        echo
        echo -e "${BLUE}管理命令：${NC}"
        echo "  查看状态: docker ps | grep $container_name"
        echo "  查看日志: docker logs $container_name"
        echo "  停止容器: docker stop $container_name"
        echo "  重启容器: docker restart $container_name"
        echo
        echo -e "${BLUE}数据目录：${NC} $data_dir"
        echo
        
        # 创建快速管理脚本
        cat > "$HOME/koishi-$KOISHI_PORT.sh" << EOF
#!/bin/bash
# Koishi 管理脚本 (端口: $KOISHI_PORT)

case "\$1" in
    start)
        echo "启动 Koishi..."
        docker start $container_name
        echo "访问地址: http://localhost:$KOISHI_PORT"
        ;;
    stop)
        echo "停止 Koishi..."
        docker stop $container_name
        ;;
    restart)
        echo "重启 Koishi..."
        docker restart $container_name
        echo "访问地址: http://localhost:$KOISHI_PORT"
        ;;
    logs)
        docker logs -f $container_name
        ;;
    status)
        docker ps | grep $container_name
        ;;
    remove)
        read -p "确认删除容器? (y/N): " confirm
        if [[ \$confirm == [yY] ]]; then
            docker rm -f $container_name
            echo "容器已删除"
        fi
        ;;
    *)
        echo "用法: \$0 {start|stop|restart|logs|status|remove}"
        echo "当前容器: $container_name"
        echo "访问地址: http://localhost:$KOISHI_PORT"
        ;;
esac
EOF
        chmod +x "$HOME/koishi-$KOISHI_PORT.sh"
        
        log "SUCCESS" "管理脚本已创建: ~/koishi-$KOISHI_PORT.sh"
        echo -e "${YELLOW}使用方法: ./koishi-$KOISHI_PORT.sh {start|stop|restart|logs|status|remove}${NC}"
        
    else
        log "ERROR" "容器启动失败"
        exit 1
    fi
}

# 主函数
main() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi Docker 快速部署"
    echo "========================================"
    echo -e "${NC}"
    
    # 解析参数
    parse_arguments "$@"
    
    # 交互式选择端口
    interactive_port_selection

    # 检查 Docker
    check_docker

    # 检测现有容器并进行交互式管理
    local detection_result=$(detect_existing_containers)
    interactive_container_management "$detection_result"

    # 快速部署
    quick_deploy
    
    log "SUCCESS" "部署完成！"
}

# 执行主函数
main "$@"
