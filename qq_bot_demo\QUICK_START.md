# QQ机器人快速启动指南

## 🚀 一键启动（推荐）

### Linux/Mac 用户

```bash
# 1. 给脚本添加执行权限
chmod +x install.sh start.sh

# 2. 安装环境
./install.sh

# 3. 启动机器人
./start.sh
```

### Windows 用户

```cmd
# 1. 安装环境
install.bat

# 2. 激活虚拟环境
qq_bot_env\Scripts\activate.bat

# 3. 启动机器人
python bot.py
```

## 🔧 手动安装步骤

### 1. 安装Python环境

**Linux (Ubuntu/Debian):**
```bash
# 安装Python3和pip
sudo apt update
sudo apt install python3 python3-pip python3-venv

# 验证安装
python3 --version
pip3 --version
```

**Windows:**
- 下载并安装Python 3.8+: https://www.python.org/downloads/
- 安装时勾选"Add Python to PATH"

### 2. 创建虚拟环境

```bash
# 创建虚拟环境
python3 -m venv qq_bot_env

# 激活虚拟环境
# Linux/Mac:
source qq_bot_env/bin/activate
# Windows:
qq_bot_env\Scripts\activate.bat
```

### 3. 安装依赖

```bash
# 升级pip
pip install --upgrade pip

# 安装QQ机器人SDK
pip install qq-botpy

# 安装其他依赖
pip install aiohttp
```

### 4. 配置机器人

你的机器人信息已经配置好了：
- **AppID**: 3889706950
- **Token**: nSmPvPwmONRSvXjsIXfNXmXmjmjk  
- **Secret**: 102000399

### 5. 测试配置

```bash
# 运行配置测试
python3 test_config.py
```

### 6. 启动机器人

```bash
# 启动基础机器人
python3 bot.py

# 或启动高级功能机器人
python3 advanced_example.py
```

## 🧪 测试机器人

### 1. 添加到测试频道

1. 登录QQ开放平台: https://bot.q.qq.com/open
2. 进入机器人管理页面
3. 配置沙箱频道
4. 扫码添加机器人到频道

### 2. 测试基础功能

在频道中发送以下消息测试：

```
@机器人名称 你好
@机器人名称 /help
@机器人名称 /ping
@机器人名称 /time
```

### 3. 测试高级功能（如果使用advanced_example.py）

```
@机器人名称 /remind 1 测试提醒
@机器人名称 /random 1 100
@机器人名称 /calc 2+3*4
@机器人名称 /joke
```

## 📝 常见问题

### Q: pip命令不存在
**A:** 
```bash
# Linux
sudo apt install python3-pip

# 或使用python3 -m pip代替pip
python3 -m pip install qq-botpy
```

### Q: 机器人无法连接
**A:** 检查以下项目：
1. 网络连接是否正常
2. AppID和Token是否正确
3. 是否配置了IP白名单
4. 防火墙设置

### Q: 收不到消息
**A:** 确认：
1. 机器人已添加到频道
2. 正确@机器人
3. 频道权限设置正确

### Q: 权限不足
**A:** 
1. 确认机器人在频道中有发送消息权限
2. 检查频道设置中的机器人权限

## 📊 项目文件说明

```
qq_bot_demo/
├── bot.py              # 基础机器人（推荐新手）
├── advanced_example.py # 高级功能机器人
├── config.py          # 配置文件（已配置）
├── test_config.py     # 配置测试脚本
├── install.sh         # Linux安装脚本
├── install.bat        # Windows安装脚本
├── start.sh           # Linux启动脚本
└── requirements.txt   # 依赖列表
```

## 🎯 下一步

1. **成功启动机器人后**，可以：
   - 添加自定义命令
   - 接入外部API（天气、翻译等）
   - 添加数据库存储
   - 部署到服务器

2. **学习资源**：
   - 查看 `resources.md` 了解更多学习资料
   - 阅读 `deployment_guide.md` 学习部署

3. **获取帮助**：
   - 加入官方QQ频道开发者社区
   - 查看官方文档: https://bot.q.qq.com/wiki/

## 🔗 重要提醒

- ⚠️ **不要泄露Token等敏感信息**
- ⚠️ **遵守QQ开放平台运营规范**
- ⚠️ **在沙箱环境充分测试后再上线**

祝你开发愉快！🎉
