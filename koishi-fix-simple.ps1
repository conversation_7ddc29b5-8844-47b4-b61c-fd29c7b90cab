# Koishi 简化修复脚本
param(
    [string]$ProjectPath = ".",
    [int]$Port = 5140
)

function Write-ColorLog {
    param(
        [string]$Level,
        [string]$Message
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Cyan }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        "WARN" { Write-Host "[$timestamp] [WARN] $Message" -ForegroundColor Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
        "FIX" { Write-Host "[$timestamp] [FIX] $Message" -ForegroundColor Yellow }
    }
}

Write-Host ""
Write-Host "=================================" -ForegroundColor Green
Write-Host "    Koishi Windows 修复工具" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""

# 步骤1：停止现有进程
Write-ColorLog "INFO" "步骤1：停止现有的 Koishi 进程..."

$nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
if ($nodeProcesses) {
    Write-ColorLog "FIX" "发现 $($nodeProcesses.Count) 个 Node.js 进程，正在停止..."
    $nodeProcesses | Stop-Process -Force
    Write-ColorLog "SUCCESS" "Node.js 进程已停止"
} else {
    Write-ColorLog "INFO" "没有发现运行中的 Node.js 进程"
}

# 检查端口占用
try {
    $portProcess = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portProcess) {
        $pid = $portProcess.OwningProcess
        Write-ColorLog "FIX" "端口 $Port 被进程 $pid 占用，正在停止..."
        Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
        Write-ColorLog "SUCCESS" "端口 $Port 已释放"
    } else {
        Write-ColorLog "SUCCESS" "端口 $Port 未被占用"
    }
} catch {
    Write-ColorLog "INFO" "无法检查端口状态"
}

Write-Host ""

# 步骤2：检查工具
Write-ColorLog "INFO" "步骤2：检查必要工具..."

$nodeExists = $false
$npmExists = $false

try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-ColorLog "SUCCESS" "Node.js 版本: $nodeVersion"
        $nodeExists = $true
    }
} catch {
    Write-ColorLog "ERROR" "Node.js 未安装"
}

try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-ColorLog "SUCCESS" "npm 版本: $npmVersion"
        $npmExists = $true
    }
} catch {
    Write-ColorLog "ERROR" "npm 未安装"
}

if (-not $nodeExists -or -not $npmExists) {
    Write-ColorLog "ERROR" "请先安装 Node.js: https://nodejs.org/"
    Write-Host ""
    Write-Host "按任意键退出..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    exit 1
}

Write-Host ""

# 步骤3：检查项目
Write-ColorLog "INFO" "步骤3：检查项目目录..."

$ProjectPath = Resolve-Path $ProjectPath -ErrorAction SilentlyContinue
if (-not $ProjectPath) {
    Write-ColorLog "ERROR" "项目路径不存在"
    exit 1
}

Write-ColorLog "INFO" "项目路径: $ProjectPath"

$packageJsonPath = Join-Path $ProjectPath "package.json"
if (-not (Test-Path $packageJsonPath)) {
    Write-ColorLog "WARN" "未找到 package.json"
    Write-ColorLog "INFO" "这可能不是一个有效的 Koishi 项目"
} else {
    Write-ColorLog "SUCCESS" "找到 package.json"
}

Write-Host ""

# 步骤4：安装依赖
Write-ColorLog "INFO" "步骤4：检查依赖..."

Set-Location $ProjectPath

$nodeModulesPath = Join-Path $ProjectPath "node_modules"
if (-not (Test-Path $nodeModulesPath)) {
    Write-ColorLog "FIX" "未找到 node_modules，正在安装..."
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-ColorLog "SUCCESS" "依赖安装完成"
    } else {
        Write-ColorLog "WARN" "依赖安装可能有问题"
    }
} else {
    Write-ColorLog "SUCCESS" "依赖已存在"
}

Write-Host ""

# 步骤5：配置防火墙
Write-ColorLog "INFO" "步骤5：配置防火墙..."

try {
    $existingRule = Get-NetFirewallRule -DisplayName "Koishi-$Port" -ErrorAction SilentlyContinue
    if ($existingRule) {
        Write-ColorLog "SUCCESS" "防火墙规则已存在"
    } else {
        Write-ColorLog "FIX" "添加防火墙规则..."
        New-NetFirewallRule -DisplayName "Koishi-$Port" -Direction Inbound -Protocol TCP -LocalPort $Port -Action Allow | Out-Null
        Write-ColorLog "SUCCESS" "防火墙规则已添加"
    }
} catch {
    Write-ColorLog "WARN" "无法配置防火墙规则，可能需要管理员权限"
}

Write-Host ""

# 步骤6：启动服务
Write-ColorLog "INFO" "步骤6：启动 Koishi..."

if (-not (Test-Path "package.json")) {
    Write-ColorLog "ERROR" "未找到 package.json，请确保在正确的项目目录中"
    exit 1
}

Write-ColorLog "FIX" "启动 Koishi 服务..."

# 启动服务
Start-Process -FilePath "npm" -ArgumentList "start" -WindowStyle Normal

Write-ColorLog "SUCCESS" "Koishi 启动命令已执行"

Write-Host ""

# 步骤7：验证访问
Write-ColorLog "INFO" "步骤7：等待服务启动..."

Start-Sleep -Seconds 10

Write-ColorLog "INFO" "尝试访问服务..."

$maxAttempts = 5
$attempt = 0
$accessSuccess = $false

while ($attempt -lt $maxAttempts -and -not $accessSuccess) {
    $attempt++
    Write-ColorLog "INFO" "尝试访问 ($attempt/$maxAttempts)..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-ColorLog "SUCCESS" "Koishi 服务访问正常！"
            $accessSuccess = $true
        }
    } catch {
        Write-ColorLog "WARN" "访问失败，等待重试..."
        if ($attempt -lt $maxAttempts) {
            Start-Sleep -Seconds 5
        }
    }
}

Write-Host ""

# 显示结果
if ($accessSuccess) {
    Write-Host "🎉 Koishi 修复完成！" -ForegroundColor Green
} else {
    Write-Host "⚠️ 服务已启动，但访问验证失败" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "访问地址：" -ForegroundColor Cyan
Write-Host "  本地访问: http://localhost:$Port"
Write-Host "  内网访问: http://127.0.0.1:$Port"

# 获取本机 IP
try {
    $localIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -ne "127.0.0.1" -and $_.PrefixOrigin -eq "Dhcp" }).IPAddress | Select-Object -First 1
    if ($localIP) {
        Write-Host "  局域网访问: http://$localIP:$Port"
    }
} catch {
    # 忽略错误
}

Write-Host ""
Write-Host "管理命令：" -ForegroundColor Cyan
Write-Host "  查看进程: Get-Process -Name node"
Write-Host "  停止服务: Stop-Process -Name node"
Write-Host ""
Write-Host "注意事项：" -ForegroundColor Yellow
Write-Host "• 首次访问可能需要几分钟初始化"
Write-Host "• 如果仍无法访问，请检查防火墙设置"
Write-Host "• 确保在正确的 Koishi 项目目录中运行"
Write-Host ""

Write-ColorLog "SUCCESS" "修复完成！"

Write-Host ""
Write-Host "按任意键退出..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
