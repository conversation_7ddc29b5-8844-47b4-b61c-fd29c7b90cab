#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动配置QQ机器人信息
用于重新输入正确的AppID、Token、AppSecret
"""

import asyncio
import aiohttp
import json


async def test_credentials(appid, token, secret):
    """测试凭据是否有效"""
    print(f"🔍 测试凭据...")
    print(f"   AppID: {appid}")
    print(f"   Token: {token[:10]}...{token[-4:]}")
    print(f"   Secret: {secret[:10]}...{secret[-4:]}")
    
    url = "https://bots.qq.com/app/getAppAccessToken"
    
    # 测试不同的参数组合
    test_configs = [
        {
            "name": "使用AppID + AppSecret",
            "data": {
                "appId": appid,
                "clientSecret": secret
            }
        },
        {
            "name": "使用AppID + Token",
            "data": {
                "appId": appid,
                "clientSecret": token
            }
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        for config in test_configs:
            print(f"\n📋 {config['name']}:")
            
            try:
                async with session.post(url, json=config['data']) as response:
                    result = await response.json()
                    print(f"   状态码: {response.status}")
                    print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    if response.status == 200 and "access_token" in result:
                        print("   ✅ 获取access_token成功！")
                        return True, config['name'], config['data']
                    else:
                        print("   ❌ 获取access_token失败")
                        
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
    
    return False, None, None


def update_config_file(appid, token, secret, working_config=None):
    """更新配置文件"""
    print(f"\n📝 更新配置文件...")
    
    # 确定正确的secret值
    if working_config:
        if "AppSecret" in working_config:
            correct_secret = secret
        else:
            correct_secret = token
    else:
        correct_secret = secret  # 默认使用secret
    
    config_content = f'''# -*- coding: utf-8 -*-
"""
QQ机器人配置文件
请在QQ开放平台获取以下信息：https://bot.q.qq.com/open
"""

# 机器人基本信息 - 手动配置
BOT_CONFIG = {{
    "appid": "{appid}",  # 你的机器人AppID
    "token": "{token}",  # 你的机器人Token
    "secret": "{correct_secret}",  # 你的机器人AppSecret (或Token)
}}

# 日志配置
LOG_CONFIG = {{
    "level": "INFO",  # 日志级别：DEBUG, INFO, WARNING, ERROR
    "format": "[%(levelname)s] %(asctime)s - %(filename)s:%(lineno)d - %(message)s",
    "file_enabled": True,  # 是否启用文件日志
    "file_path": "logs/bot.log"  # 日志文件路径
}}

# 机器人功能配置
FEATURES = {{
    "auto_reply": True,  # 自动回复功能
    "command_prefix": "/",  # 命令前缀
    "welcome_message": "你好！我是QQ机器人，输入 /help 查看帮助信息",
    "help_message": """
🤖 机器人帮助信息：

📝 基础命令：
/help - 显示帮助信息
/ping - 测试机器人响应
/time - 获取当前时间
/weather [城市] - 查询天气（示例功能）

💬 聊天功能：
直接@我发送消息即可聊天

❓ 如有问题，请联系管理员
    """
}}

# API配置
API_CONFIG = {{
    "timeout": 30,  # 请求超时时间（秒）
    "retry_times": 3,  # 重试次数
}}'''
    
    try:
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ 配置文件更新成功！")
        return True
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False


async def main():
    """主函数"""
    print("🔧 QQ机器人手动配置工具")
    print("=" * 50)
    print("请从QQ开放平台重新获取以下信息：")
    print("https://bot.q.qq.com/open")
    print("=" * 50)
    
    # 手动输入信息
    print("📝 请输入机器人信息：")
    appid = input("AppID (机器人AppID): ").strip()
    token = input("Token (机器人Token): ").strip()
    secret = input("AppSecret (机器人AppSecret): ").strip()
    
    if not all([appid, token, secret]):
        print("❌ 信息不完整，请重新运行脚本")
        return
    
    print("\n" + "=" * 50)
    
    # 测试凭据
    success, working_method, working_data = await test_credentials(appid, token, secret)
    
    if success:
        print(f"\n🎉 找到有效的配置方法: {working_method}")
        
        # 更新配置文件
        if update_config_file(appid, token, secret, working_method):
            print("\n✅ 配置完成！现在可以运行机器人了：")
            print("python3 bot.py")
        
    else:
        print("\n❌ 所有配置方法都失败了")
        print("\n🔍 请检查：")
        print("1. AppID是否正确")
        print("2. Token是否正确且完整")
        print("3. AppSecret是否正确且完整")
        print("4. 机器人是否已在QQ开放平台创建成功")
        print("5. 机器人状态是否正常")
        
        # 仍然更新配置文件，以便后续调试
        update_config_file(appid, token, secret)
        
        print("\n💡 建议：")
        print("1. 登录QQ开放平台重新检查机器人信息")
        print("2. 尝试重新生成Token和AppSecret")
        print("3. 确认机器人已通过审核（如果需要）")


if __name__ == "__main__":
    asyncio.run(main())
