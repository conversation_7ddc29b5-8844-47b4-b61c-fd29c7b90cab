#!/bin/bash

# Koishi 插件创建脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
    esac
}

# 检查依赖
check_dependencies() {
    log "INFO" "检查依赖环境..."
    
    # 检查 Node.js
    if ! command -v node >/dev/null 2>&1; then
        log "ERROR" "Node.js 未安装，请先安装 Node.js 18+"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        log "ERROR" "Node.js 版本过低，需要 18+，当前版本: $(node --version)"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm >/dev/null 2>&1; then
        log "ERROR" "npm 未安装"
        exit 1
    fi
    
    log "SUCCESS" "依赖检查通过"
}

# 获取用户输入
get_user_input() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi 插件创建向导"
    echo "========================================"
    echo -e "${NC}"
    
    # 插件名称
    while true; do
        read -p "请输入插件名称 (不含 koishi-plugin- 前缀): " PLUGIN_NAME
        if [[ "$PLUGIN_NAME" =~ ^[a-z0-9-]+$ ]]; then
            break
        else
            log "ERROR" "插件名称只能包含小写字母、数字和连字符"
        fi
    done
    
    # 插件描述
    read -p "请输入插件描述: " PLUGIN_DESCRIPTION
    if [ -z "$PLUGIN_DESCRIPTION" ]; then
        PLUGIN_DESCRIPTION="A Koishi plugin"
    fi
    
    # 作者信息
    read -p "请输入作者姓名: " AUTHOR_NAME
    if [ -z "$AUTHOR_NAME" ]; then
        AUTHOR_NAME="Unknown"
    fi
    
    read -p "请输入作者邮箱: " AUTHOR_EMAIL
    if [ -z "$AUTHOR_EMAIL" ]; then
        AUTHOR_EMAIL="<EMAIL>"
    fi
    
    # GitHub 用户名
    read -p "请输入 GitHub 用户名 (可选): " GITHUB_USERNAME
    if [ -z "$GITHUB_USERNAME" ]; then
        GITHUB_USERNAME="yourusername"
    fi
    
    # 创建方式
    echo
    echo "请选择创建方式:"
    echo "1) 创建新的 Koishi 项目 (推荐)"
    echo "2) 在现有 Koishi 项目中创建插件"
    echo "3) 仅创建插件模板文件"
    
    while true; do
        read -p "请选择 (1-3): " CREATE_MODE
        case $CREATE_MODE in
            1|2|3) break ;;
            *) log "ERROR" "请输入 1、2 或 3" ;;
        esac
    done
    
    # 确认信息
    echo
    echo -e "${YELLOW}确认信息：${NC}"
    echo "插件名称: koishi-plugin-$PLUGIN_NAME"
    echo "插件描述: $PLUGIN_DESCRIPTION"
    echo "作者: $AUTHOR_NAME <$AUTHOR_EMAIL>"
    echo "GitHub: https://github.com/$GITHUB_USERNAME/koishi-plugin-$PLUGIN_NAME"
    echo "创建方式: $CREATE_MODE"
    echo
    
    read -p "确认创建? (y/N): " CONFIRM
    if [[ ! $CONFIRM == [yY] ]]; then
        log "INFO" "用户取消操作"
        exit 0
    fi
}

# 创建新的 Koishi 项目
create_new_project() {
    local project_name="koishi-bot-$PLUGIN_NAME"
    
    log "INFO" "创建新的 Koishi 项目: $project_name"
    
    # 使用官方模板创建项目
    npm init koishi@latest "$project_name" -- --template basic
    
    if [ $? -ne 0 ]; then
        log "ERROR" "创建 Koishi 项目失败"
        exit 1
    fi
    
    cd "$project_name"
    PROJECT_DIR=$(pwd)
    
    log "SUCCESS" "Koishi 项目创建成功: $PROJECT_DIR"
}

# 创建插件文件
create_plugin_files() {
    local plugin_dir
    
    if [ "$CREATE_MODE" = "1" ]; then
        plugin_dir="$PROJECT_DIR/plugins/$PLUGIN_NAME"
    elif [ "$CREATE_MODE" = "2" ]; then
        if [ ! -f "koishi.yml" ]; then
            log "ERROR" "当前目录不是 Koishi 项目根目录"
            exit 1
        fi
        plugin_dir="./plugins/$PLUGIN_NAME"
    else
        plugin_dir="./$PLUGIN_NAME"
    fi
    
    log "INFO" "创建插件目录: $plugin_dir"
    mkdir -p "$plugin_dir/src/locales"
    
    # 创建 package.json
    cat > "$plugin_dir/package.json" << EOF
{
  "name": "koishi-plugin-$PLUGIN_NAME",
  "version": "1.0.0",
  "description": "$PLUGIN_DESCRIPTION",
  "main": "lib/index.js",
  "typings": "lib/index.d.ts",
  "files": [
    "lib",
    "dist"
  ],
  "license": "MIT",
  "author": "$AUTHOR_NAME <$AUTHOR_EMAIL>",
  "homepage": "https://github.com/$GITHUB_USERNAME/koishi-plugin-$PLUGIN_NAME",
  "repository": {
    "type": "git",
    "url": "git+https://github.com/$GITHUB_USERNAME/koishi-plugin-$PLUGIN_NAME.git"
  },
  "bugs": {
    "url": "https://github.com/$GITHUB_USERNAME/koishi-plugin-$PLUGIN_NAME/issues"
  },
  "keywords": [
    "chatbot",
    "koishi",
    "plugin"
  ],
  "peerDependencies": {
    "koishi": "^4.15.0"
  },
  "devDependencies": {
    "@types/node": "^18.0.0",
    "typescript": "^4.9.0"
  },
  "koishi": {
    "description": {
      "en": "$PLUGIN_DESCRIPTION",
      "zh": "$PLUGIN_DESCRIPTION"
    },
    "service": {
      "required": [],
      "optional": [],
      "implements": []
    },
    "locales": ["en", "zh"]
  }
}
EOF
    
    # 创建主文件
    cat > "$plugin_dir/src/index.ts" << 'EOF'
import { Context, Schema } from 'koishi'

export const name = 'PLUGIN_NAME_PLACEHOLDER'

export interface Config {
  prefix: string
  enableLogging: boolean
}

export const Config: Schema<Config> = Schema.object({
  prefix: Schema.string().default('Bot').description('回复前缀'),
  enableLogging: Schema.boolean().default(true).description('启用日志记录'),
})

export function apply(ctx: Context, config: Config) {
  const logger = ctx.logger('PLUGIN_NAME_PLACEHOLDER')
  
  // 注册命令
  ctx.command('PLUGIN_NAME_PLACEHOLDER', 'PLUGIN_DESCRIPTION_PLACEHOLDER')
    .action(({ session }) => {
      if (config.enableLogging) {
        logger.info(`User ${session.username} used PLUGIN_NAME_PLACEHOLDER command`)
      }
      return `${config.prefix}: Hello from PLUGIN_NAME_PLACEHOLDER plugin! 👋`
    })
  
  // 监听消息
  ctx.on('message', (session) => {
    if (session.content === 'hello') {
      session.send(`${config.prefix}: Hello, ${session.username}!`)
    }
  })
  
  logger.info('PLUGIN_NAME_PLACEHOLDER plugin loaded successfully')
}
EOF
    
    # 替换占位符
    sed -i "s/PLUGIN_NAME_PLACEHOLDER/$PLUGIN_NAME/g" "$plugin_dir/src/index.ts"
    sed -i "s/PLUGIN_DESCRIPTION_PLACEHOLDER/$PLUGIN_DESCRIPTION/g" "$plugin_dir/src/index.ts"
    
    # 创建 TypeScript 配置
    cat > "$plugin_dir/tsconfig.json" << EOF
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "declaration": true,
    "outDir": "lib",
    "rootDir": "src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "allowSyntheticDefaultImports": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "resolveJsonModule": true
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "lib",
    "dist"
  ]
}
EOF
    
    # 创建语言包
    cat > "$plugin_dir/src/locales/zh.yml" << EOF
commands:
  $PLUGIN_NAME:
    description: $PLUGIN_DESCRIPTION
    messages:
      hello: "{0}: 你好，{1}！👋"

messages:
  welcome: "欢迎使用 $PLUGIN_NAME 插件！"
EOF
    
    cat > "$plugin_dir/src/locales/en.yml" << EOF
commands:
  $PLUGIN_NAME:
    description: $PLUGIN_DESCRIPTION
    messages:
      hello: "{0}: Hello, {1}! 👋"

messages:
  welcome: "Welcome to $PLUGIN_NAME plugin!"
EOF
    
    # 创建 README
    cat > "$plugin_dir/README.md" << EOF
# koishi-plugin-$PLUGIN_NAME

$PLUGIN_DESCRIPTION

## 安装

\`\`\`bash
npm install koishi-plugin-$PLUGIN_NAME
\`\`\`

## 使用

在 Koishi 配置文件中添加插件：

\`\`\`yaml
plugins:
  $PLUGIN_NAME:
    prefix: "Bot"
    enableLogging: true
\`\`\`

## 命令

- \`$PLUGIN_NAME\` - 测试命令

## 配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| \`prefix\` | string | "Bot" | 回复前缀 |
| \`enableLogging\` | boolean | true | 启用日志记录 |

## 许可证

MIT
EOF
    
    log "SUCCESS" "插件文件创建完成: $plugin_dir"
}

# 构建和测试
build_and_test() {
    if [ "$CREATE_MODE" = "1" ] || [ "$CREATE_MODE" = "2" ]; then
        log "INFO" "构建插件..."
        
        if [ "$CREATE_MODE" = "1" ]; then
            cd "$PROJECT_DIR"
        fi
        
        # 构建插件
        npm run build
        
        if [ $? -eq 0 ]; then
            log "SUCCESS" "插件构建成功"
        else
            log "WARN" "插件构建失败，请检查代码"
        fi
        
        # 启动测试 (可选)
        echo
        read -p "是否启动 Koishi 进行测试? (y/N): " START_TEST
        if [[ $START_TEST == [yY] ]]; then
            log "INFO" "启动 Koishi..."
            npm start
        fi
    fi
}

# 显示完成信息
show_completion_info() {
    echo
    echo -e "${GREEN}🎉 插件创建完成！${NC}"
    echo
    echo -e "${BLUE}插件信息：${NC}"
    echo "名称: koishi-plugin-$PLUGIN_NAME"
    echo "描述: $PLUGIN_DESCRIPTION"
    echo "作者: $AUTHOR_NAME"
    echo
    
    if [ "$CREATE_MODE" = "1" ]; then
        echo -e "${BLUE}项目目录：${NC}"
        echo "$PROJECT_DIR"
        echo
        echo -e "${BLUE}启动命令：${NC}"
        echo "cd $PROJECT_DIR"
        echo "npm start"
    elif [ "$CREATE_MODE" = "2" ]; then
        echo -e "${BLUE}插件目录：${NC}"
        echo "./plugins/$PLUGIN_NAME"
        echo
        echo -e "${BLUE}启动命令：${NC}"
        echo "npm start"
    else
        echo -e "${BLUE}插件目录：${NC}"
        echo "./$PLUGIN_NAME"
        echo
        echo -e "${BLUE}下一步：${NC}"
        echo "1. 将插件复制到 Koishi 项目的 plugins 目录"
        echo "2. 在 Koishi 项目中运行 npm run build"
        echo "3. 启动 Koishi: npm start"
    fi
    
    echo
    echo -e "${BLUE}开发命令：${NC}"
    echo "npm run build     # 构建插件"
    echo "npm run bump      # 更新版本"
    echo "npm run pub       # 发布插件"
    echo
    echo -e "${BLUE}有用的资源：${NC}"
    echo "- Koishi 文档: https://koishi.chat/"
    echo "- 插件开发指南: https://koishi.chat/guide/"
    echo "- 插件市场: https://koishi.chat/market/"
    echo
    echo -e "${GREEN}祝您开发愉快！${NC}"
}

# 主函数
main() {
    check_dependencies
    get_user_input
    
    if [ "$CREATE_MODE" = "1" ]; then
        create_new_project
    fi
    
    create_plugin_files
    build_and_test
    show_completion_info
}

main "$@"
