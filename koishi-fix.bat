@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo =================================
echo     Koishi Windows 修复工具
echo =================================
echo.

:: 设置颜色
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARN=[WARN]"
set "ERROR=[ERROR]"
set "FIX=[FIX]"

:: 步骤1：停止现有进程
echo %INFO% 步骤1：停止现有的 Koishi 进程...

tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if %ERRORLEVEL%==0 (
    echo %FIX% 发现 Node.js 进程，正在停止...
    taskkill /F /IM node.exe >nul 2>&1
    echo %SUCCESS% Node.js 进程已停止
) else (
    echo %INFO% 没有发现运行中的 Node.js 进程
)

:: 检查端口占用
netstat -ano | findstr :5140 >nul
if %ERRORLEVEL%==0 (
    echo %FIX% 端口 5140 被占用，尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5140') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    echo %SUCCESS% 端口 5140 已释放
) else (
    echo %SUCCESS% 端口 5140 未被占用
)

echo.

:: 步骤2：检查工具
echo %INFO% 步骤2：检查必要工具...

node --version >nul 2>&1
if %ERRORLEVEL%==0 (
    for /f %%i in ('node --version') do echo %SUCCESS% Node.js 版本: %%i
) else (
    echo %ERROR% Node.js 未安装
    echo %ERROR% 请先安装 Node.js: https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %ERRORLEVEL%==0 (
    for /f %%i in ('npm --version') do echo %SUCCESS% npm 版本: %%i
) else (
    echo %ERROR% npm 未安装
    pause
    exit /b 1
)

echo.

:: 步骤3：检查项目
echo %INFO% 步骤3：检查项目目录...
echo %INFO% 项目路径: %CD%

if exist "package.json" (
    echo %SUCCESS% 找到 package.json
) else (
    echo %WARN% 未找到 package.json
    echo %INFO% 这可能不是一个有效的 Koishi 项目
)

echo.

:: 步骤4：安装依赖
echo %INFO% 步骤4：检查依赖...

if exist "node_modules" (
    echo %SUCCESS% 依赖已存在
) else (
    echo %FIX% 未找到 node_modules，正在安装...
    npm install
    if %ERRORLEVEL%==0 (
        echo %SUCCESS% 依赖安装完成
    ) else (
        echo %WARN% 依赖安装可能有问题
    )
)

echo.

:: 步骤5：配置防火墙
echo %INFO% 步骤5：配置防火墙...

netsh advfirewall firewall show rule name="Koishi-5140" >nul 2>&1
if %ERRORLEVEL%==0 (
    echo %SUCCESS% 防火墙规则已存在
) else (
    echo %FIX% 添加防火墙规则...
    netsh advfirewall firewall add rule name="Koishi-5140" dir=in action=allow protocol=TCP localport=5140 >nul 2>&1
    if %ERRORLEVEL%==0 (
        echo %SUCCESS% 防火墙规则已添加
    ) else (
        echo %WARN% 无法配置防火墙规则，可能需要管理员权限
    )
)

echo.

:: 步骤6：启动服务
echo %INFO% 步骤6：启动 Koishi...

if not exist "package.json" (
    echo %ERROR% 未找到 package.json，请确保在正确的项目目录中
    pause
    exit /b 1
)

echo %FIX% 启动 Koishi 服务...

:: 启动服务
start "Koishi" cmd /c "npm start"

echo %SUCCESS% Koishi 启动命令已执行

echo.

:: 步骤7：验证访问
echo %INFO% 步骤7：等待服务启动...

timeout /t 10 /nobreak >nul

echo %INFO% 尝试访问服务...

set "attempt=0"
set "maxAttempts=5"
set "accessSuccess=0"

:checkAccess
set /a attempt+=1
echo %INFO% 尝试访问 (!attempt!/!maxAttempts!)...

:: 使用 curl 检查访问（如果可用）
curl -s -o nul -w "%%{http_code}" http://localhost:5140 2>nul | findstr "200" >nul
if %ERRORLEVEL%==0 (
    echo %SUCCESS% Koishi 服务访问正常！
    set "accessSuccess=1"
    goto :accessResult
)

:: 使用 PowerShell 检查访问
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5140' -TimeoutSec 5 -ErrorAction Stop; if ($response.StatusCode -eq 200) { exit 0 } else { exit 1 } } catch { exit 1 }" >nul 2>&1
if %ERRORLEVEL%==0 (
    echo %SUCCESS% Koishi 服务访问正常！
    set "accessSuccess=1"
    goto :accessResult
)

echo %WARN% 访问失败，等待重试...
if !attempt! lss !maxAttempts! (
    timeout /t 5 /nobreak >nul
    goto :checkAccess
)

:accessResult
echo.

:: 显示结果
if !accessSuccess!==1 (
    echo 🎉 Koishi 修复完成！
) else (
    echo ⚠️ 服务已启动，但访问验证失败
)

echo.
echo 访问地址：
echo   本地访问: http://localhost:5140
echo   内网访问: http://127.0.0.1:5140

:: 获取本机 IP
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /C:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        echo   局域网访问: http://%%b:5140
        goto :ipFound
    )
)
:ipFound

echo.
echo 管理命令：
echo   查看进程: tasklist /FI "IMAGENAME eq node.exe"
echo   停止服务: taskkill /F /IM node.exe
echo.
echo 注意事项：
echo • 首次访问可能需要几分钟初始化
echo • 如果仍无法访问，请检查防火墙设置
echo • 确保在正确的 Koishi 项目目录中运行
echo.

echo %SUCCESS% 修复完成！

echo.
echo 按任意键退出...
pause >nul
