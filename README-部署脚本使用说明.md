# Koishi 一键部署脚本使用说明

## 📋 脚本概述

这是一个功能完整的 Koishi 聊天机器人框架一键部署脚本，支持在主流 Linux 发行版上自动安装和配置 Koishi。

### ✨ 主要特性

- **🔍 智能检测**: 自动识别 Linux 发行版和系统环境
- **📦 完整安装**: 自动安装 Node.js 18+、npm、Yarn 和 Koishi
- **🌐 网络优化**: 支持国内镜像源加速下载
- **🛡️ 安全配置**: 自动配置防火墙规则和安全设置
- **🔧 服务管理**: 可选创建 systemd 服务实现开机自启
- **📊 详细日志**: 完整的安装日志和错误处理
- **🎯 用户友好**: 清晰的进度提示和交互式配置

### 🖥️ 支持的系统

| 发行版 | 版本要求 | 包管理器 | 状态 |
|--------|----------|----------|------|
| Ubuntu | 18.04+ | APT | ✅ 完全支持 |
| Debian | 10+ | APT | ✅ 完全支持 |
| CentOS | 7+ | YUM/DNF | ✅ 完全支持 |
| RHEL | 7+ | YUM/DNF | ✅ 完全支持 |
| Rocky Linux | 8+ | DNF | ✅ 完全支持 |
| Fedora | 30+ | DNF | ✅ 完全支持 |
| openSUSE | Leap 15+ | Zypper | ✅ 完全支持 |
| Arch Linux | 滚动更新 | Pacman | ✅ 完全支持 |
| Manjaro | 最新版 | Pacman | ✅ 完全支持 |

### 📋 系统要求

#### 最低要求
- **CPU**: x86_64 或 aarch64 架构
- **内存**: 1GB RAM（推荐 2GB+）
- **存储**: 2GB 可用磁盘空间
- **网络**: 稳定的互联网连接
- **权限**: sudo 权限

#### 软件依赖
- `curl` 或 `wget`（用于下载）
- `bash` 4.0+
- 系统包管理器（apt/yum/dnf/zypper/pacman）

## 🚀 快速开始

### 方法一：一行命令安装（推荐）

```bash
curl -fsSL https://raw.githubusercontent.com/your-repo/koishi-deploy.sh | bash
```

### 方法二：下载后执行

```bash
# 下载脚本
wget https://raw.githubusercontent.com/your-repo/koishi-deploy.sh
# 或者
curl -O https://raw.githubusercontent.com/your-repo/koishi-deploy.sh

# 添加执行权限
chmod +x koishi-deploy.sh

# 运行脚本
./koishi-deploy.sh
```

### 方法三：查看帮助信息

```bash
./koishi-deploy.sh --help
./koishi-deploy.sh --version
```

## 📖 详细使用指南

### 1. 脚本执行流程

脚本采用五个阶段的安装流程：

#### 🔍 系统检查阶段
- 检测操作系统类型和版本
- 验证系统要求（内存、磁盘、网络）
- 检查必要的权限和依赖

#### 📦 系统准备阶段
- 更新系统包管理器
- 安装基础依赖包和开发工具
- 配置编译环境

#### 🟢 Node.js 环境阶段
- 智能检测和安装 Node.js 18+
- 配置 npm 镜像源和全局目录
- 可选安装 Yarn 包管理器

#### 🤖 Koishi 部署阶段
- 创建项目目录和基础结构
- 安装 Koishi 核心和基础插件
- 生成配置文件和启动脚本

#### ⚙️ 系统配置阶段
- 配置防火墙规则开放端口
- 可选创建 systemd 服务
- 设置正确的文件权限

#### ✅ 验证测试阶段
- 验证所有组件安装正确
- 可选进行启动测试
- 显示完成信息和使用指南

### 2. 交互式配置选项

脚本会在运行过程中询问以下配置：

#### 📁 项目配置
- **项目名称**: 默认为 `koishi-bot`，只能包含字母、数字、下划线和连字符
- **安装路径**: 自动设置为 `$HOME/项目名称`

#### 🌐 网络配置
- **镜像源选择**: 自动检测地理位置，中国用户推荐使用国内镜像源
- **下载加速**: 配置 npm 和 Yarn 的镜像源

#### 🔧 可选组件
- **Yarn 安装**: 是否安装 Yarn 包管理器（推荐）
- **防火墙配置**: 是否自动配置防火墙规则
- **系统服务**: 是否创建 systemd 服务实现开机自启

### 3. 生成的文件结构

安装完成后，项目目录结构如下：

```
~/koishi-bot/
├── koishi.yml          # 主配置文件
├── package.json        # 项目依赖配置
├── package-lock.json   # 依赖锁定文件
├── start.sh           # 启动脚本
├── .gitignore         # Git 忽略文件
├── node_modules/      # 依赖包目录
├── data/             # 数据存储目录
│   └── koishi.db     # SQLite 数据库
└── logs/             # 日志目录
```

### 4. 配置文件说明

#### koishi.yml 主要配置项

```yaml
# 服务器配置
host: 0.0.0.0          # 监听地址
port: 5140             # 监听端口

# 数据库配置
database:
  type: sqlite         # 数据库类型
  path: ./data/koishi.db

# 基础插件配置
plugins:
  console: {}          # 控制台插件
  dataview: {}         # 数据视图
  status: {}           # 状态监控
  commands: {}         # 指令管理
  help: {}             # 帮助系统
  rate-limit: {}       # 频率限制
  locales: {}          # 本地化
```

## 🔧 安装后配置

### 1. 启动 Koishi

#### 手动启动
```bash
cd ~/koishi-bot
npm start
# 或者
./start.sh
```

#### 使用系统服务（如果已创建）
```bash
# 启动服务
sudo systemctl start koishi-koishi-bot

# 停止服务
sudo systemctl stop koishi-koishi-bot

# 重启服务
sudo systemctl restart koishi-koishi-bot

# 查看状态
sudo systemctl status koishi-koishi-bot

# 查看日志
sudo journalctl -u koishi-koishi-bot -f
```

### 2. 访问控制台

启动成功后，在浏览器中访问：
- 本地访问：`http://localhost:5140`
- 远程访问：`http://your-server-ip:5140`

### 3. 基础配置

#### 安装适配器插件
在控制台的插件市场中搜索并安装适配器插件：
- QQ：`@koishijs/plugin-adapter-onebot`
- 微信：`@koishijs/plugin-adapter-wechat`
- Telegram：`@koishijs/plugin-adapter-telegram`
- Discord：`@koishijs/plugin-adapter-discord`

#### 配置机器人账号
1. 在控制台中启用相应的适配器插件
2. 填写机器人的登录凭据
3. 保存配置并重启

### 4. 安全配置建议

#### 防火墙配置
```bash
# 如果脚本未自动配置，手动配置防火墙
sudo ufw allow 5140/tcp                    # Ubuntu/Debian
sudo firewall-cmd --add-port=5140/tcp      # CentOS/RHEL/Fedora
```

#### 反向代理配置（生产环境推荐）
使用 Nginx 配置反向代理和 HTTPS：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:5140;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🐛 故障排除

### 常见问题及解决方案

#### 1. Node.js 版本问题
**问题**: 提示 Node.js 版本过低
**解决**: 脚本会自动安装最新 LTS 版本，如果失败可手动安装：
```bash
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 2. 网络连接问题
**问题**: 下载速度慢或连接超时
**解决**: 
- 选择使用国内镜像源
- 检查网络连接和防火墙设置
- 配置代理（如果需要）

#### 3. 权限问题
**问题**: 安装时出现权限错误
**解决**:
```bash
# 确保有 sudo 权限
sudo -v

# 修复 npm 权限
sudo chown -R $(whoami) $(npm config get prefix)/{lib/node_modules,bin,share}
```

#### 4. 端口占用问题
**问题**: 端口 5140 被占用
**解决**:
```bash
# 查看占用进程
sudo netstat -tlnp | grep :5140
sudo lsof -i :5140

# 修改配置文件中的端口
nano ~/koishi-bot/koishi.yml
```

#### 5. 服务启动失败
**问题**: systemd 服务无法启动
**解决**:
```bash
# 查看服务状态
sudo systemctl status koishi-koishi-bot

# 查看详细日志
sudo journalctl -u koishi-koishi-bot -n 50

# 检查配置文件
sudo systemctl cat koishi-koishi-bot
```

### 日志文件位置

- **安装日志**: `/tmp/koishi-deploy-YYYYMMDD_HHMMSS.log`
- **运行日志**: `~/koishi-bot/logs/`
- **系统日志**: `sudo journalctl -u koishi-koishi-bot`

## 📚 相关资源

### 官方文档
- [Koishi 官网](https://koishi.chat/)
- [快速开始](https://koishi.chat/zh-CN/manual/starter/)
- [插件开发](https://koishi.chat/zh-CN/guide/plugin/)
- [API 文档](https://koishi.chat/zh-CN/api/)

### 社区资源
- [GitHub 仓库](https://github.com/koishijs/koishi)
- [插件市场](https://koishi.chat/zh-CN/market/)
- [社区讨论](https://koishi.chat/zh-CN/about/contact.html)
- [问题反馈](https://github.com/koishijs/koishi/issues)

## 🤝 贡献和支持

如果您在使用过程中遇到问题或有改进建议，欢迎：

1. 提交 Issue 报告问题
2. 提交 Pull Request 贡献代码
3. 在社区论坛寻求帮助
4. 分享使用经验和最佳实践

---

**脚本版本**: v2.0.0  
**更新时间**: 2024-08-01  
**维护者**: Koishi 社区
