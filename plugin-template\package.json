{"name": "koishi-plugin-example", "version": "1.0.0", "description": "一个功能丰富的 Koishi 插件示例", "main": "lib/index.js", "typings": "lib/index.d.ts", "files": ["lib", "dist"], "license": "MIT", "author": "Your Name <<EMAIL>>", "homepage": "https://github.com/yourusername/koishi-plugin-example", "repository": {"type": "git", "url": "git+https://github.com/yourusername/koishi-plugin-example.git"}, "bugs": {"url": "https://github.com/yourusername/koishi-plugin-example/issues"}, "keywords": ["chatbot", "koishi", "plugin", "example", "template"], "peerDependencies": {"koishi": "^4.15.0"}, "devDependencies": {"@types/node": "^18.0.0", "typescript": "^4.9.0"}, "koishi": {"description": {"en": "A feature-rich Koishi plugin example", "zh": "一个功能丰富的 Koishi 插件示例"}, "service": {"required": [], "optional": ["database"], "implements": []}, "locales": ["en", "zh"], "preview": false, "hidden": false}}