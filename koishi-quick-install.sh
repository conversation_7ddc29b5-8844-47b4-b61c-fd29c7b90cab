#!/bin/bash

#==============================================================================
# Koishi 快速安装脚本 (简化版)
# 
# 这是一个简化版的 Koishi 安装脚本，适合快速部署和测试
# 如需完整功能，请使用 koishi-deploy.sh
#
# 使用方法：
#   curl -fsSL https://raw.githubusercontent.com/your-repo/koishi-quick-install.sh | bash
#
# 版本：v1.0.0
#==============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_NAME="koishi-bot"
KOISHI_PORT=5140
MIN_NODE_VERSION=18

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    
    case "$level" in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查 Node.js 版本
check_nodejs() {
    if command_exists node; then
        local version=$(node --version | sed 's/v//')
        local major=$(echo "$version" | cut -d. -f1)
        
        if [ "$major" -ge "$MIN_NODE_VERSION" ]; then
            log "SUCCESS" "Node.js 版本符合要求: v$version"
            return 0
        else
            log "WARNING" "Node.js 版本过低: v$version (需要 >= v$MIN_NODE_VERSION)"
            return 1
        fi
    else
        log "INFO" "未检测到 Node.js"
        return 1
    fi
}

# 安装 Node.js
install_nodejs() {
    log "INFO" "安装 Node.js..."
    
    if check_nodejs; then
        return 0
    fi
    
    # 检测系统类型
    if [ -f /etc/debian_version ]; then
        # Ubuntu/Debian
        curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL/Fedora
        curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
        sudo yum install -y nodejs || sudo dnf install -y nodejs
    elif command_exists pacman; then
        # Arch Linux
        sudo pacman -S --noconfirm nodejs npm
    else
        log "ERROR" "不支持的操作系统，请手动安装 Node.js"
        exit 1
    fi
    
    if check_nodejs; then
        log "SUCCESS" "Node.js 安装成功"
    else
        log "ERROR" "Node.js 安装失败"
        exit 1
    fi
}

# 配置 npm
configure_npm() {
    log "INFO" "配置 npm..."
    
    # 检测是否在中国
    if curl -s --connect-timeout 3 --max-time 5 "http://ip-api.com/json" | grep -q '"country":"China"' 2>/dev/null; then
        log "INFO" "检测到中国网络环境，配置国内镜像源"
        npm config set registry https://registry.npmmirror.com
    fi
    
    # 配置全局目录
    local npm_global="$HOME/.npm-global"
    mkdir -p "$npm_global"
    npm config set prefix "$npm_global"
    
    # 添加到 PATH
    if ! grep -q "$npm_global/bin" "$HOME/.bashrc" 2>/dev/null; then
        echo "export PATH=$npm_global/bin:\$PATH" >> "$HOME/.bashrc"
        export PATH="$npm_global/bin:$PATH"
    fi
    
    log "SUCCESS" "npm 配置完成"
}

# 创建 Koishi 项目
create_project() {
    log "INFO" "创建 Koishi 项目..."
    
    local project_path="$HOME/$PROJECT_NAME"
    
    # 检查目录是否存在
    if [ -d "$project_path" ]; then
        log "WARNING" "目录 $project_path 已存在"
        read -p "是否删除并重新创建？(y/N): " -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -rf "$project_path"
        else
            log "ERROR" "安装已取消"
            exit 1
        fi
    fi
    
    # 创建项目
    mkdir -p "$project_path"
    cd "$project_path"
    
    # 初始化 package.json
    cat > package.json << EOF
{
  "name": "$PROJECT_NAME",
  "version": "1.0.0",
  "description": "Koishi Bot Project",
  "main": "index.js",
  "scripts": {
    "start": "koishi start",
    "dev": "koishi start --watch"
  },
  "keywords": ["koishi", "bot"],
  "license": "MIT"
}
EOF
    
    # 安装 Koishi
    log "INFO" "安装 Koishi 和基础插件..."
    npm install koishi @koishijs/plugin-console @koishijs/plugin-dataview @koishijs/plugin-status
    
    # 创建配置文件
    cat > koishi.yml << EOF
host: 0.0.0.0
port: $KOISHI_PORT

database:
  type: sqlite
  path: ./data/koishi.db

plugins:
  console:
    open: false
  dataview: {}
  status: {}

logger:
  levels:
    base: 2
EOF
    
    # 创建数据目录
    mkdir -p data logs
    
    # 创建启动脚本
    cat > start.sh << 'EOF'
#!/bin/bash
echo "启动 Koishi..."
npm start
EOF
    chmod +x start.sh
    
    # 创建 .gitignore
    cat > .gitignore << EOF
node_modules/
data/
logs/
*.log
.env
EOF
    
    log "SUCCESS" "项目创建完成: $project_path"
}

# 配置防火墙
configure_firewall() {
    log "INFO" "配置防火墙..."
    
    if command_exists ufw; then
        sudo ufw allow "$KOISHI_PORT/tcp" >/dev/null 2>&1 && \
        log "SUCCESS" "UFW 防火墙规则已添加"
    elif command_exists firewall-cmd; then
        sudo firewall-cmd --permanent --add-port="$KOISHI_PORT/tcp" >/dev/null 2>&1 && \
        sudo firewall-cmd --reload >/dev/null 2>&1 && \
        log "SUCCESS" "firewalld 防火墙规则已添加"
    elif command_exists iptables; then
        sudo iptables -A INPUT -p tcp --dport "$KOISHI_PORT" -j ACCEPT >/dev/null 2>&1 && \
        log "SUCCESS" "iptables 防火墙规则已添加"
    else
        log "WARNING" "未检测到防火墙，请手动开放端口 $KOISHI_PORT"
    fi
}

# 显示完成信息
show_completion() {
    echo
    echo -e "${GREEN}🎉 Koishi 快速安装完成！${NC}"
    echo
    echo -e "${BLUE}项目路径：${NC}$HOME/$PROJECT_NAME"
    echo -e "${BLUE}访问地址：${NC}http://localhost:$KOISHI_PORT"
    echo
    echo -e "${YELLOW}启动命令：${NC}"
    echo "  cd $HOME/$PROJECT_NAME"
    echo "  npm start"
    echo "  # 或者"
    echo "  ./start.sh"
    echo
    echo -e "${YELLOW}重要提示：${NC}"
    echo "• 首次启动需要几分钟初始化"
    echo "• 访问控制台配置机器人账号"
    echo "• 更多插件请访问插件市场"
    echo
    echo -e "${BLUE}官方文档：${NC}https://koishi.chat/"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "========================================"
    echo "    Koishi 快速安装脚本 v1.0.0"
    echo "========================================"
    echo -e "${NC}"
    
    # 检查系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log "ERROR" "此脚本仅支持 Linux 系统"
        exit 1
    fi
    
    # 检查必要命令
    for cmd in curl; do
        if ! command_exists "$cmd"; then
            log "ERROR" "缺少必要命令: $cmd"
            exit 1
        fi
    done
    
    # 执行安装
    install_nodejs
    configure_npm
    create_project
    configure_firewall
    show_completion
    
    log "SUCCESS" "安装完成！"
}

# 执行主函数
main "$@"
