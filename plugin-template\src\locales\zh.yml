# 中文语言包
commands:
  example:
    description: 示例插件命令
    
    hello:
      description: 打招呼
      usage: example.hello [姓名]
      messages:
        greeting: "{0}: 你好，{1}！👋"
    
    points:
      description: 积分系统
      
      check:
        description: 查看积分
        messages:
          current: "{0}: 你的积分: {1} 💰"
          error: "查询积分失败，请稍后重试。"
      
      signin:
        description: 每日签到
        messages:
          success: "{0}: 签到成功！获得 {1} 积分 ✨\n当前积分: {2}"
          already: "今天已经签到过了！明天再来吧 📅"
          error: "签到失败，请稍后重试。"
    
    random:
      description: 生成随机数
      usage: example.random [-m 最小值] [-M 最大值]
      messages:
        result: "{0}: 随机数: {1} 🎲"
        invalid: "最小值必须小于最大值！"
    
    admin:
      description: 管理员命令
      
      reset:
        description: 重置用户积分
        usage: example.admin.reset <用户ID>
        messages:
          success: "{0}: 已重置用户 {1} 的积分。"
          error: "重置积分失败。"
      
      logs:
        description: 查看操作日志
        usage: example.admin.logs [数量]
        messages:
          header: "{0}: 最近 {1} 条日志:"
          empty: "暂无日志记录。"
          error: "获取日志失败。"

errors:
  permission_denied: "权限不足：需要管理员权限。"
  message_too_long: "消息太长了！最大长度为 {0} 字符。"
  database_error: "数据库操作失败，请稍后重试。"
  invalid_input: "输入无效，请检查参数格式。"

events:
  welcome: "{0}: 欢迎 {1} 加入群组！🎉\n输入 \"example.hello\" 开始使用插件功能。"
  
messages:
  points_query: "你的积分: {0} 💰"
  
config:
  prefix:
    description: "回复消息的前缀"
  maxMessageLength:
    description: "最大消息长度限制"
  enableLogging:
    description: "是否启用详细日志记录"
  adminUsers:
    description: "管理员用户ID列表"
  pointsConfig:
    description: "积分系统配置"
    dailyBonus:
      description: "每日签到奖励积分"
    maxPoints:
      description: "积分上限"
    enableReset:
      description: "是否允许重置积分"
