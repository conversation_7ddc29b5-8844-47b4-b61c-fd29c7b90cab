@echo off
chcp 65001 >nul
echo 🚀 开始安装QQ机器人环境...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

REM 检查pip是否可用
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pip不可用，请检查Python安装
    pause
    exit /b 1
)

echo ✅ pip可用
pip --version

REM 创建虚拟环境
echo 🔧 创建Python虚拟环境...
python -m venv qq_bot_env

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call qq_bot_env\Scripts\activate.bat

REM 升级pip
echo ⬆️ 升级pip...
python -m pip install --upgrade pip

REM 安装项目依赖
echo 📚 安装项目依赖...
pip install qq-botpy aiohttp

REM 创建logs目录
echo 📁 创建日志目录...
if not exist logs mkdir logs

echo ✅ 环境安装完成！
echo.
echo 🎯 下一步操作：
echo 1. 激活虚拟环境: qq_bot_env\Scripts\activate.bat
echo 2. 运行机器人: python bot.py
echo.
echo 📝 注意: 请确保已在config.py中配置了正确的机器人信息
pause
