#!/bin/bash

# 查找 Koishi 配置文件位置脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

show_header() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi 配置文件位置查找工具"
    echo "========================================"
    echo -e "${NC}"
}

# 查找 Docker 容器
find_koishi_containers() {
    log "INFO" "查找 Koishi Docker 容器..."
    
    # 查找所有可能的 Koishi 容器
    local containers=$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "koishi|5140" | grep -v "NAMES")
    
    if [ -n "$containers" ]; then
        echo -e "${BLUE}找到的 Koishi 容器：${NC}"
        echo "$containers"
        echo
        
        # 获取容器名称
        local container_names=$(docker ps -a --format "{{.Names}}" | grep -E "koishi")
        
        for container in $container_names; do
            log "INFO" "检查容器: $container"
            
            # 检查容器状态
            local status=$(docker ps -a --format "{{.Status}}" --filter "name=$container")
            echo "  状态: $status"
            
            # 如果容器在运行，查找配置文件
            if docker ps --format "{{.Names}}" | grep -q "^$container$"; then
                log "SUCCESS" "容器 $container 正在运行"
                find_config_in_container "$container"
            else
                log "WARN" "容器 $container 未运行"
            fi
            echo
        done
    else
        log "WARN" "未找到 Koishi 相关容器"
        return 1
    fi
}

# 在容器中查找配置文件
find_config_in_container() {
    local container_name=$1
    
    log "INFO" "在容器 $container_name 中查找配置文件..."
    
    # 可能的配置文件路径
    local config_paths=(
        "/koishi/koishi.yml"
        "/koishi/data/koishi.yml"
        "/app/koishi.yml"
        "/usr/src/app/koishi.yml"
        "/koishi/koishi.yaml"
        "/koishi/config.yml"
    )
    
    for path in "${config_paths[@]}"; do
        if docker exec "$container_name" test -f "$path" 2>/dev/null; then
            log "SUCCESS" "找到配置文件: $path"
            
            # 显示文件信息
            local file_info=$(docker exec "$container_name" ls -la "$path" 2>/dev/null)
            echo "  文件信息: $file_info"
            
            # 显示文件内容预览
            echo -e "${YELLOW}  配置文件预览 (前10行):${NC}"
            docker exec "$container_name" head -10 "$path" 2>/dev/null | sed 's/^/    /'
            echo
        fi
    done
    
    # 查找工作目录
    local workdir=$(docker exec "$container_name" pwd 2>/dev/null)
    if [ -n "$workdir" ]; then
        log "INFO" "容器工作目录: $workdir"
        
        # 在工作目录查找配置文件
        local workdir_configs=$(docker exec "$container_name" find "$workdir" -name "*.yml" -o -name "*.yaml" 2>/dev/null | head -5)
        if [ -n "$workdir_configs" ]; then
            echo -e "${BLUE}  工作目录中的配置文件:${NC}"
            echo "$workdir_configs" | sed 's/^/    /'
        fi
    fi
}

# 查找宿主机映射目录
find_host_mappings() {
    log "INFO" "查找宿主机数据目录映射..."
    
    # 查找可能的数据目录
    local data_dirs=(
        "$HOME/koishi-data"
        "$HOME/koishi-data-5140"
        "$HOME/koishi-data-8080"
        "./koishi-data"
        "/var/lib/koishi"
    )
    
    for dir in "${data_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log "SUCCESS" "找到数据目录: $dir"
            
            # 查找配置文件
            if [ -f "$dir/koishi.yml" ]; then
                log "SUCCESS" "找到配置文件: $dir/koishi.yml"
                echo -e "${YELLOW}  配置文件预览 (前5行):${NC}"
                head -5 "$dir/koishi.yml" | sed 's/^/    /'
            fi
            
            # 显示目录内容
            echo -e "${BLUE}  目录内容:${NC}"
            ls -la "$dir" | head -10 | sed 's/^/    /'
            echo
        fi
    done
}

# 提供修复建议
provide_solutions() {
    echo
    log "INFO" "配置文件修复方案..."
    echo
    
    echo -e "${YELLOW}方案1: 直接在容器中修改配置${NC}"
    echo "# 进入容器"
    echo "docker exec -it <容器名称> /bin/bash"
    echo "# 编辑配置文件"
    echo "vi /koishi/koishi.yml"
    echo
    
    echo -e "${YELLOW}方案2: 从宿主机复制配置文件到容器${NC}"
    echo "# 复制优化配置到容器"
    echo "docker cp pixluna-fixes/optimized-config.yml <容器名称>:/koishi/koishi.yml"
    echo "# 重启容器"
    echo "docker restart <容器名称>"
    echo
    
    echo -e "${YELLOW}方案3: 使用数据卷映射${NC}"
    echo "# 如果有数据目录映射"
    echo "cp pixluna-fixes/optimized-config.yml ~/koishi-data/koishi.yml"
    echo "docker restart <容器名称>"
    echo
    
    echo -e "${YELLOW}方案4: 重新创建容器并映射配置${NC}"
    echo "# 停止现有容器"
    echo "docker stop <容器名称>"
    echo "# 创建新容器并映射配置文件"
    echo "docker run -d \\"
    echo "  --name koishi-bot \\"
    echo "  -p 5140:5140 \\"
    echo "  -v \$(pwd)/pixluna-fixes/optimized-config.yml:/koishi/koishi.yml \\"
    echo "  -v ~/koishi-data:/koishi/data \\"
    echo "  koishijs/koishi:latest"
}

# 创建配置应用脚本
create_apply_script() {
    log "INFO" "创建配置应用脚本..."
    
    cat > "apply-docker-config.sh" << 'EOF'
#!/bin/bash

# Docker 环境下应用 pixluna 配置修复

echo "选择应用方式："
echo "1) 复制到运行中的容器"
echo "2) 复制到数据目录"
echo "3) 显示手动操作指令"

read -p "请选择 (1-3): " choice

case $choice in
    1)
        # 查找容器
        container=$(docker ps --format "{{.Names}}" | grep koishi | head -1)
        if [ -n "$container" ]; then
            echo "找到容器: $container"
            echo "复制配置文件..."
            docker cp pixluna-fixes/optimized-config.yml "$container":/koishi/koishi.yml
            echo "重启容器..."
            docker restart "$container"
            echo "配置已应用！"
        else
            echo "未找到运行中的 Koishi 容器"
        fi
        ;;
    2)
        # 查找数据目录
        if [ -d "$HOME/koishi-data" ]; then
            echo "复制到数据目录..."
            cp pixluna-fixes/optimized-config.yml "$HOME/koishi-data/koishi.yml"
            echo "重启容器..."
            docker restart $(docker ps --format "{{.Names}}" | grep koishi | head -1)
            echo "配置已应用！"
        else
            echo "未找到数据目录"
        fi
        ;;
    3)
        echo "手动操作指令："
        echo "docker cp pixluna-fixes/optimized-config.yml <容器名>:/koishi/koishi.yml"
        echo "docker restart <容器名>"
        ;;
esac
EOF
    
    chmod +x apply-docker-config.sh
    log "SUCCESS" "已创建 apply-docker-config.sh 脚本"
}

# 主函数
main() {
    show_header
    
    log "INFO" "开始查找 Koishi 配置文件位置..."
    echo
    
    # 检查 Docker
    if ! command -v docker >/dev/null 2>&1; then
        log "ERROR" "Docker 未安装或不可用"
        exit 1
    fi
    
    # 查找容器和配置
    find_koishi_containers
    find_host_mappings
    
    # 提供解决方案
    provide_solutions
    
    # 创建应用脚本
    create_apply_script
    
    echo
    log "SUCCESS" "查找完成！"
    echo
    echo -e "${GREEN}下一步操作：${NC}"
    echo "1. 根据上述信息确定配置文件位置"
    echo "2. 运行 ./apply-docker-config.sh 应用修复"
    echo "3. 或手动复制配置文件到容器"
    echo "4. 重启容器使配置生效"
}

main "$@"
