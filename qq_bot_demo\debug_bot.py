#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版QQ机器人
用于诊断消息接收问题
"""

import asyncio
import logging
import traceback
import random
from datetime import datetime

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class DebugBot(botpy.Client):
    """调试版QQ机器人"""
    
    def __init__(self):
        # 设置事件订阅 - 订阅所有可能的事件
        intents = botpy.Intents(
            public_guild_messages=True,  # 公域消息事件
            direct_message=True,  # 私信事件
            guild_messages=True,  # 频道消息事件
        )
        
        super().__init__(intents=intents)
        
        # 设置详细日志
        logging.basicConfig(
            level=logging.DEBUG,  # 使用DEBUG级别
            format='[%(levelname)s] %(asctime)s - %(filename)s:%(lineno)d - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/debug_bot.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        print("🔧 调试模式启动，将显示详细日志")

    async def on_ready(self):
        """机器人启动完成事件"""
        self.logger.info(f"调试机器人 {self.robot.name} 已上线！")
        print("=" * 60)
        print(f"🎉 调试机器人启动成功！")
        print(f"🤖 机器人名称: {self.robot.name}")
        print(f"🆔 机器人ID: {self.robot.id}")
        print(f"📱 机器人QQ号: 3889706980")
        print("=" * 60)
        print("🔍 调试模式已启用，将显示所有事件")
        print("📝 请在频道中@机器人发送消息进行测试")
        print("=" * 60)

    async def on_message_create(self, message: Message):
        """处理所有消息事件（包括非@消息）"""
        try:
            print(f"📩 收到消息事件: {message.content}")
            print(f"   作者: {message.author.username if message.author else 'Unknown'}")
            print(f"   频道ID: {message.channel_id}")
            print(f"   消息ID: {message.id}")
            self.logger.info(f"收到消息: {message.content}")
            
            # 检查是否@了机器人
            if f"<@!{self.robot.id}>" in message.content:
                print("🎯 检测到@机器人消息！")
                await self.handle_at_message(message)
            else:
                print("ℹ️ 普通消息，未@机器人")
                
        except Exception as e:
            print(f"❌ 处理消息事件时发生错误: {e}")
            self.logger.error(f"处理消息事件错误: {e}")

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息"""
        try:
            print(f"📨 收到@消息事件: {message.content}")
            print(f"   作者: {message.author.username if message.author else 'Unknown'}")
            print(f"   频道ID: {message.channel_id}")
            self.logger.info(f"收到@消息: {message.content}")
            
            await self.handle_at_message(message)
                
        except Exception as e:
            print(f"❌ 处理@消息时发生错误: {e}")
            self.logger.error(f"处理@消息错误: {e}")
            traceback.print_exc()

    async def on_direct_message_create(self, message: Message):
        """处理私信消息"""
        try:
            print(f"💬 收到私信: {message.content}")
            self.logger.info(f"收到私信: {message.content}")
            
            await self.handle_at_message(message)
                
        except Exception as e:
            print(f"❌ 处理私信时发生错误: {e}")
            self.logger.error(f"处理私信错误: {e}")

    async def handle_at_message(self, message: Message):
        """统一处理@消息"""
        try:
            # 提取消息内容
            content = self.extract_content(message.content)
            print(f"💭 提取的内容: '{content}'")
            
            # 生成回复
            if "你好" in content.lower() or "hello" in content.lower() or "hi" in content.lower():
                replies = [
                    "你好！很高兴见到你！😊",
                    "嗨！今天过得怎么样？🌟",
                    "你好呀！有什么我可以帮助你的吗？😄",
                    "Hello！欢迎来聊天！👋",
                    "你好！我是QQ机器人，很开心认识你！🤖"
                ]
                reply = random.choice(replies)
                print(f"🎲 随机选择回复: {reply}")
            else:
                reply = f"我收到了你的消息: {content} 😊"
                print(f"📝 默认回复: {reply}")
            
            # 发送回复
            print(f"📤 准备发送回复...")
            await self.send_message(message, reply)
            
        except Exception as e:
            print(f"❌ 处理消息内容时发生错误: {e}")
            traceback.print_exc()

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容，去除@机器人部分"""
        import re
        print(f"🔍 原始消息: '{raw_content}'")
        
        # 去除@机器人的部分
        content = re.sub(r'<@!\d+>\s*', '', raw_content).strip()
        print(f"🔍 处理后消息: '{content}'")
        
        return content

    async def send_message(self, original_message: Message, content: str):
        """发送消息"""
        try:
            print(f"📤 发送消息到频道 {original_message.channel_id}")
            print(f"📝 消息内容: {content}")
            
            # 发送消息
            result = await self.api.post_message(
                channel_id=original_message.channel_id,
                content=content,
                msg_id=original_message.id
            )
            
            print(f"✅ 消息发送成功！")
            print(f"📋 发送结果: {result}")
            self.logger.info(f"发送消息成功: {content}")
            
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            print(f"📋 错误详情:")
            traceback.print_exc()
            self.logger.error(f"发送消息失败: {e}")

    async def on_guild_create(self, guild):
        """频道创建事件"""
        print(f"🏰 频道事件: {guild}")

    async def on_guild_update(self, guild):
        """频道更新事件"""
        print(f"🔄 频道更新: {guild}")

    async def on_channel_create(self, channel):
        """子频道创建事件"""
        print(f"📺 子频道创建: {channel}")

    async def on_error(self, error):
        """错误事件"""
        print(f"💥 发生错误: {error}")
        traceback.print_exc()


def main():
    """主函数"""
    print("🔧 正在启动调试版QQ机器人...")
    print("=" * 60)
    print(f"📱 AppID: {BOT_CONFIG['appid']}")
    print(f"🔑 使用已验证的AppSecret")
    print("🔍 调试模式: 启用")
    print("=" * 60)
    
    # 创建机器人实例
    bot = DebugBot()
    
    try:
        print("🔄 使用AppSecret启动机器人...")
        bot.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 详细错误信息:")
        traceback.print_exc()


if __name__ == "__main__":
    main()
