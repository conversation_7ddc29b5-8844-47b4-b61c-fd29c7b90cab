# Koishi 聊天机器人框架 Linux 一键部署指南

## 目录
- [系统要求和前置条件](#系统要求和前置条件)
- [一键自动化安装脚本](#一键自动化安装脚本)
- [手动安装说明](#手动安装说明)
- [常见问题排查](#常见问题排查)
- [部署后配置和验证](#部署后配置和验证)
- [相关官方文档链接](#相关官方文档链接)

## 系统要求和前置条件

### 支持的 Linux 发行版
- **Ubuntu**: 18.04 LTS 及以上版本
- **Debian**: 10 (Buster) 及以上版本
- **CentOS**: 7 及以上版本
- **RHEL**: 7 及以上版本
- **Fedora**: 30 及以上版本
- **openSUSE**: Leap 15.0 及以上版本
- **Arch Linux**: 滚动更新版本

### 系统要求
- **CPU**: x86_64 架构，最低 1 核心
- **内存**: 最低 512MB RAM，推荐 1GB 及以上
- **存储**: 最低 2GB 可用空间
- **网络**: 稳定的互联网连接

### Node.js 版本要求
- **最低版本**: Node.js v18.0.0
- **推荐版本**: Node.js LTS (当前为 v20.x)
- **不支持**: Node.js v17 及以下版本

### 前置软件包
```bash
# 基础工具
curl wget git unzip

# 编译工具（某些插件可能需要）
build-essential python3 make g++
```

## 一键自动化安装脚本

### 完整安装脚本

创建并保存以下脚本为 `install-koishi.sh`：

```bash
#!/bin/bash

# Koishi 聊天机器人框架一键安装脚本
# 支持 Ubuntu, Debian, CentOS, RHEL, Fedora, openSUSE, Arch Linux

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    else
        log_error "无法检测操作系统类型"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log_warning "检测到 root 用户，建议使用普通用户运行此脚本"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 更新系统包管理器
update_system() {
    log_info "更新系统包管理器..."
    
    case $OS in
        *Ubuntu*|*Debian*)
            sudo apt update && sudo apt upgrade -y
            sudo apt install -y curl wget git unzip build-essential python3 make g++
            ;;
        *CentOS*|*"Red Hat"*|*RHEL*)
            sudo yum update -y
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y curl wget git unzip python3 make gcc-c++
            ;;
        *Fedora*)
            sudo dnf update -y
            sudo dnf groupinstall -y "Development Tools"
            sudo dnf install -y curl wget git unzip python3 make gcc-c++
            ;;
        *openSUSE*)
            sudo zypper refresh
            sudo zypper update -y
            sudo zypper install -y curl wget git unzip python3 make gcc-c++ patterns-devel-base-devel_basis
            ;;
        *Arch*)
            sudo pacman -Syu --noconfirm
            sudo pacman -S --noconfirm curl wget git unzip python base-devel
            ;;
        *)
            log_warning "未识别的操作系统，跳过系统包更新"
            ;;
    esac
    
    log_success "系统包更新完成"
}

# 安装 Node.js
install_nodejs() {
    log_info "检查 Node.js 安装状态..."
    
    # 检查是否已安装 Node.js
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version | sed 's/v//')
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d. -f1)
        
        if [ "$MAJOR_VERSION" -ge 18 ]; then
            log_success "Node.js $NODE_VERSION 已安装且版本符合要求"
            return 0
        else
            log_warning "Node.js 版本过低 ($NODE_VERSION)，需要升级到 v18+"
        fi
    fi
    
    log_info "安装 Node.js LTS 版本..."
    
    # 使用 NodeSource 仓库安装 Node.js
    curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
    
    case $OS in
        *Ubuntu*|*Debian*)
            sudo apt-get install -y nodejs
            ;;
        *CentOS*|*"Red Hat"*|*RHEL*|*Fedora*)
            curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
            sudo yum install -y nodejs
            ;;
        *openSUSE*)
            curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
            sudo zypper install -y nodejs
            ;;
        *Arch*)
            sudo pacman -S --noconfirm nodejs npm
            ;;
        *)
            # 通用安装方法：使用 n 版本管理器
            curl -fsSL https://raw.githubusercontent.com/tj/n/master/bin/n | sudo bash -s lts
            ;;
    esac
    
    # 验证安装
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version)
        NPM_VERSION=$(npm --version)
        log_success "Node.js $NODE_VERSION 和 npm $NPM_VERSION 安装成功"
    else
        log_error "Node.js 安装失败"
        exit 1
    fi
}

# 配置 npm 镜像源（针对中国用户）
configure_npm_registry() {
    log_info "配置 npm 镜像源..."
    
    # 检测网络连接到 npm 官方源的速度
    if timeout 5 curl -s https://registry.npmjs.org/ >/dev/null 2>&1; then
        log_info "npm 官方源连接正常"
        read -p "是否配置国内镜像源以提升下载速度？(Y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Nn]$ ]]; then
            return 0
        fi
    fi
    
    # 配置淘宝镜像源
    npm config set registry https://registry.npmmirror.com
    log_success "npm 镜像源配置完成"
}

# 安装 yarn（可选）
install_yarn() {
    log_info "安装 yarn 包管理器..."
    
    if command -v yarn >/dev/null 2>&1; then
        log_success "yarn 已安装"
        return 0
    fi
    
    npm install -g yarn
    
    if command -v yarn >/dev/null 2>&1; then
        YARN_VERSION=$(yarn --version)
        log_success "yarn $YARN_VERSION 安装成功"
    else
        log_warning "yarn 安装失败，将使用 npm"
    fi
}

# 创建 Koishi 项目
create_koishi_project() {
    log_info "创建 Koishi 项目..."
    
    # 询问项目目录
    read -p "请输入项目目录名称 (默认: koishi-bot): " PROJECT_NAME
    PROJECT_NAME=${PROJECT_NAME:-koishi-bot}
    
    # 检查目录是否存在
    if [ -d "$PROJECT_NAME" ]; then
        log_warning "目录 $PROJECT_NAME 已存在"
        read -p "是否删除并重新创建？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$PROJECT_NAME"
        else
            log_error "安装取消"
            exit 1
        fi
    fi
    
    # 创建项目
    log_info "正在创建 Koishi 项目，请按照提示进行配置..."
    
    # 使用 npm 创建项目
    npm init koishi@latest "$PROJECT_NAME"
    
    if [ -d "$PROJECT_NAME" ]; then
        cd "$PROJECT_NAME"
        log_success "Koishi 项目创建成功"
        
        # 显示项目信息
        log_info "项目路径: $(pwd)"
        log_info "启动命令: npm start"
        
        return 0
    else
        log_error "项目创建失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查防火墙状态
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu/Debian UFW
        sudo ufw allow 5140/tcp
        log_success "UFW 防火墙规则已添加 (端口 5140)"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS/RHEL/Fedora firewalld
        sudo firewall-cmd --permanent --add-port=5140/tcp
        sudo firewall-cmd --reload
        log_success "firewalld 防火墙规则已添加 (端口 5140)"
    elif command -v iptables >/dev/null 2>&1; then
        # 通用 iptables
        sudo iptables -A INPUT -p tcp --dport 5140 -j ACCEPT
        log_success "iptables 防火墙规则已添加 (端口 5140)"
    else
        log_warning "未检测到防火墙，请手动开放端口 5140"
    fi
}

# 创建系统服务（可选）
create_systemd_service() {
    read -p "是否创建 systemd 服务以便开机自启？(Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        return 0
    fi
    
    log_info "创建 systemd 服务..."
    
    SERVICE_NAME="koishi-bot"
    SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"
    PROJECT_PATH=$(pwd)
    USER_NAME=$(whoami)
    
    sudo tee "$SERVICE_FILE" > /dev/null <<EOF
[Unit]
Description=Koishi Bot Service
After=network.target

[Service]
Type=simple
User=$USER_NAME
WorkingDirectory=$PROJECT_PATH
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10
Environment=NODE_ENV=production

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    
    log_success "systemd 服务创建完成"
    log_info "服务管理命令:"
    log_info "  启动服务: sudo systemctl start $SERVICE_NAME"
    log_info "  停止服务: sudo systemctl stop $SERVICE_NAME"
    log_info "  查看状态: sudo systemctl status $SERVICE_NAME"
    log_info "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
}

# 主函数
main() {
    echo "========================================"
    echo "  Koishi 聊天机器人框架一键安装脚本"
    echo "========================================"
    echo
    
    detect_os
    check_root
    update_system
    install_nodejs
    configure_npm_registry
    install_yarn
    create_koishi_project
    configure_firewall
    create_systemd_service
    
    echo
    log_success "Koishi 安装完成！"
    echo
    log_info "接下来的步骤:"
    log_info "1. 进入项目目录: cd $PROJECT_NAME"
    log_info "2. 启动 Koishi: npm start"
    log_info "3. 访问控制台: http://localhost:5140"
    log_info "4. 如需外网访问，请确保服务器安全组开放 5140 端口"
    echo
    log_info "更多信息请访问官方文档: https://koishi.chat/"
}

# 执行主函数
main "$@"
```

### 使用方法

1. **下载并运行脚本**：
```bash
# 下载脚本
curl -fsSL -o install-koishi.sh https://raw.githubusercontent.com/your-repo/install-koishi.sh

# 或者直接创建脚本文件
nano install-koishi.sh
# 将上述脚本内容粘贴进去

# 添加执行权限
chmod +x install-koishi.sh

# 运行脚本
./install-koishi.sh
```

2. **一行命令安装**：
```bash
curl -fsSL https://raw.githubusercontent.com/your-repo/install-koishi.sh | bash
```

## 手动安装说明

如果自动化脚本无法正常工作，您可以按照以下步骤手动安装 Koishi。

### 步骤 1: 安装 Node.js

#### Ubuntu/Debian 系统
```bash
# 更新包管理器
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git unzip build-essential python3 make g++

# 添加 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -

# 安装 Node.js
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

#### CentOS/RHEL/Fedora 系统
```bash
# CentOS/RHEL
sudo yum update -y
sudo yum groupinstall -y "Development Tools"
sudo yum install -y curl wget git unzip python3 make gcc-c++

# Fedora
sudo dnf update -y
sudo dnf groupinstall -y "Development Tools"
sudo dnf install -y curl wget git unzip python3 make gcc-c++

# 添加 NodeSource 仓库并安装 Node.js
curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
sudo yum install -y nodejs  # 或 sudo dnf install -y nodejs (Fedora)

# 验证安装
node --version
npm --version
```

#### Arch Linux 系统
```bash
# 更新系统
sudo pacman -Syu --noconfirm

# 安装必要包
sudo pacman -S --noconfirm nodejs npm curl wget git unzip python base-devel

# 验证安装
node --version
npm --version
```

### 步骤 2: 配置 npm（可选但推荐）

```bash
# 配置国内镜像源（提升下载速度）
npm config set registry https://registry.npmmirror.com

# 安装 yarn（可选）
npm install -g yarn

# 验证 yarn 安装
yarn --version
```

### 步骤 3: 创建 Koishi 项目

```bash
# 创建项目目录
mkdir -p ~/koishi-bot
cd ~/koishi-bot

# 初始化 Koishi 项目
npm init koishi@latest .

# 或者使用 yarn
yarn create koishi .
```

### 步骤 4: 启动 Koishi

```bash
# 启动开发服务器
npm start

# 或者使用 yarn
yarn start
```

### 步骤 5: 配置防火墙

#### Ubuntu/Debian (UFW)
```bash
# 开放 5140 端口
sudo ufw allow 5140/tcp

# 启用防火墙（如果未启用）
sudo ufw enable

# 查看状态
sudo ufw status
```

#### CentOS/RHEL/Fedora (firewalld)
```bash
# 开放 5140 端口
sudo firewall-cmd --permanent --add-port=5140/tcp
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports
```

#### 通用方法 (iptables)
```bash
# 开放 5140 端口
sudo iptables -A INPUT -p tcp --dport 5140 -j ACCEPT

# 保存规则（Ubuntu/Debian）
sudo iptables-save > /etc/iptables/rules.v4

# 保存规则（CentOS/RHEL）
sudo service iptables save
```

## 常见问题排查

### 问题 1: Node.js 版本过低

**症状**: 安装时提示 Node.js 版本不兼容

**解决方案**:
```bash
# 检查当前版本
node --version

# 如果版本低于 v18，需要升级
# 使用 n 版本管理器升级
sudo npm install -g n
sudo n lts

# 或者重新安装 Node.js
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 问题 2: npm 安装速度慢

**症状**: npm install 命令执行缓慢或超时

**解决方案**:
```bash
# 配置国内镜像源
npm config set registry https://registry.npmmirror.com

# 或者使用 cnpm
npm install -g cnpm --registry=https://registry.npmmirror.com
cnpm install

# 清除 npm 缓存
npm cache clean --force
```

### 问题 3: 权限错误

**症状**: 安装时出现 EACCES 权限错误

**解决方案**:
```bash
# 方法 1: 配置 npm 全局目录
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 方法 2: 修改 npm 目录权限
sudo chown -R $(whoami) $(npm config get prefix)/{lib/node_modules,bin,share}
```

### 问题 4: 端口被占用

**症状**: 启动时提示端口 5140 被占用

**解决方案**:
```bash
# 查看占用端口的进程
sudo netstat -tlnp | grep :5140
# 或者
sudo lsof -i :5140

# 杀死占用进程
sudo kill -9 <PID>

# 或者修改 Koishi 配置使用其他端口
# 编辑 koishi.yml 文件，修改 port 配置
```

### 问题 5: 内存不足

**症状**: 安装或运行时出现内存不足错误

**解决方案**:
```bash
# 创建交换文件（临时解决方案）
sudo fallocate -l 1G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 永久添加到 /etc/fstab
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# 检查内存使用情况
free -h
```

### 问题 6: 网络连接问题

**症状**: 无法访问插件市场或下载插件

**解决方案**:
```bash
# 检查网络连接
ping koishi.chat
curl -I https://registry.koishi.chat

# 配置代理（如果需要）
npm config set proxy http://proxy-server:port
npm config set https-proxy http://proxy-server:port

# 或者在环境变量中设置
export HTTP_PROXY=http://proxy-server:port
export HTTPS_PROXY=http://proxy-server:port
```

### 问题 7: 插件安装失败

**症状**: 在控制台中安装插件时失败

**解决方案**:
```bash
# 进入项目目录
cd ~/koishi-bot

# 手动安装插件
npm install koishi-plugin-<plugin-name>

# 或者使用 yarn
yarn add koishi-plugin-<plugin-name>

# 清除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install
```

## 部署后配置和验证

### 基本配置验证

#### 1. 验证 Koishi 服务状态
```bash
# 检查 Koishi 是否正常运行
curl -I http://localhost:5140

# 预期输出应包含 HTTP/1.1 200 OK
```

#### 2. 访问控制台
打开浏览器访问：`http://localhost:5140`

如果是远程服务器，访问：`http://your-server-ip:5140`

#### 3. 检查日志
```bash
# 查看 Koishi 运行日志
cd ~/koishi-bot
npm start

# 如果使用了 systemd 服务
sudo journalctl -u koishi-bot -f
```

### 基础配置步骤

#### 1. 配置文件说明
Koishi 的主要配置文件是 `koishi.yml`，位于项目根目录：

```yaml
# koishi.yml 示例配置
host: 0.0.0.0  # 监听所有网络接口
port: 5140     # 控制台端口

# 数据库配置（可选）
database:
  host: localhost
  port: 3306
  user: koishi
  password: your_password
  database: koishi

# 插件配置
plugins:
  # 控制台插件（必需）
  console:
    open: false  # 是否自动打开浏览器

  # 其他插件配置...
```

#### 2. 安全配置

**修改默认端口**（推荐）:
```yaml
# koishi.yml
port: 8080  # 改为其他端口
```

**配置访问控制**:
```yaml
# koishi.yml
console:
  # 设置访问密码
  auth:
    username: admin
    password: your_secure_password
```

**配置 HTTPS**（生产环境推荐）:
```yaml
# koishi.yml
ssl:
  cert: /path/to/certificate.crt
  key: /path/to/private.key
```

#### 3. 数据库配置

**SQLite（默认，适合小型部署）**:
```yaml
database:
  type: sqlite
  path: ./data/koishi.db
```

**MySQL/MariaDB（推荐用于生产环境）**:
```bash
# 安装 MySQL
sudo apt install mysql-server  # Ubuntu/Debian
sudo yum install mysql-server   # CentOS/RHEL

# 创建数据库和用户
mysql -u root -p
CREATE DATABASE koishi;
CREATE USER 'koishi'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON koishi.* TO 'koishi'@'localhost';
FLUSH PRIVILEGES;
```

```yaml
# koishi.yml
database:
  type: mysql
  host: localhost
  port: 3306
  user: koishi
  password: your_password
  database: koishi
```

#### 4. 反向代理配置（可选）

**使用 Nginx**:
```bash
# 安装 Nginx
sudo apt install nginx  # Ubuntu/Debian
sudo yum install nginx  # CentOS/RHEL

# 创建配置文件
sudo nano /etc/nginx/sites-available/koishi
```

```nginx
# /etc/nginx/sites-available/koishi
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5140;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# 启用配置
sudo ln -s /etc/nginx/sites-available/koishi /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 功能验证清单

#### ✅ 基础功能验证
- [ ] Koishi 服务正常启动
- [ ] 控制台可以正常访问
- [ ] 插件市场可以正常加载
- [ ] 可以安装和启用插件
- [ ] 数据库连接正常

#### ✅ 网络功能验证
- [ ] 本地访问正常 (localhost:5140)
- [ ] 远程访问正常（如果需要）
- [ ] 防火墙规则配置正确
- [ ] 反向代理配置正常（如果使用）

#### ✅ 性能验证
- [ ] 内存使用在合理范围内
- [ ] CPU 使用率正常
- [ ] 响应时间可接受
- [ ] 日志无异常错误

### 生产环境优化建议

#### 1. 性能优化
```bash
# 设置 Node.js 环境变量
export NODE_ENV=production

# 增加 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=2048"
```

#### 2. 日志管理
```bash
# 配置日志轮转
sudo nano /etc/logrotate.d/koishi
```

```
/var/log/koishi/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 koishi koishi
    postrotate
        systemctl reload koishi-bot
    endscript
}
```

#### 3. 监控配置
```bash
# 安装 htop 监控系统资源
sudo apt install htop

# 监控 Koishi 进程
ps aux | grep koishi
```

#### 4. 备份策略
```bash
# 创建备份脚本
nano backup-koishi.sh
```

```bash
#!/bin/bash
# Koishi 备份脚本

BACKUP_DIR="/backup/koishi"
PROJECT_DIR="$HOME/koishi-bot"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"

# 备份配置文件和数据
tar -czf "$BACKUP_DIR/koishi_backup_$DATE.tar.gz" \
    -C "$PROJECT_DIR" \
    koishi.yml \
    data/ \
    plugins/ \
    package.json \
    package-lock.json

echo "备份完成: $BACKUP_DIR/koishi_backup_$DATE.tar.gz"
```

```bash
# 设置定时备份
crontab -e
# 添加以下行（每天凌晨 2 点备份）
0 2 * * * /path/to/backup-koishi.sh
```

## 相关官方文档链接

### 官方文档
- **Koishi 官方网站**: https://koishi.chat/
- **入门指南**: https://koishi.chat/zh-CN/manual/introduction.html
- **Linux 安装指南**: https://koishi.chat/zh-CN/manual/starter/linux.html
- **创建模板项目**: https://koishi.chat/zh-CN/manual/starter/boilerplate.html
- **Docker 部署**: https://koishi.chat/zh-CN/manual/starter/docker.html

### 开发文档
- **开发指南**: https://koishi.chat/zh-CN/guide/
- **API 参考**: https://koishi.chat/zh-CN/api/
- **插件开发**: https://koishi.chat/zh-CN/guide/plugin/
- **进阶指南**: https://koishi.chat/zh-CN/cookbook/

### 插件和市场
- **插件市场**: https://koishi.chat/zh-CN/market/
- **官方插件**: https://koishi.chat/zh-CN/plugins/
- **插件搜索**: https://koishi.chat/zh-CN/manual/recipe/search.html

### 社区资源
- **GitHub 仓库**: https://github.com/koishijs/koishi
- **社区讨论**: https://koishi.chat/zh-CN/about/contact.html
- **社区资源**: https://koishi.chat/zh-CN/about/community.html
- **问题反馈**: https://github.com/koishijs/koishi/issues

### 配置和使用
- **配置插件**: https://koishi.chat/zh-CN/manual/usage/market.html
- **适配器配置**: https://koishi.chat/zh-CN/manual/usage/adapter.html
- **指令系统**: https://koishi.chat/zh-CN/manual/usage/command.html
- **公网部署**: https://koishi.chat/zh-CN/manual/recipe/server.html

### 技术支持
- **系统要求**: https://koishi.chat/zh-CN/manual/launcher/system.html
- **命令行工具**: https://koishi.chat/zh-CN/manual/launcher/cli.html
- **故障排除**: https://koishi.chat/zh-CN/manual/recipe/
- **版本发布**: https://koishi.chat/zh-CN/releases/

---

## 总结

本指南提供了在 Linux 系统上部署 Koishi 聊天机器人框架的完整解决方案，包括：

1. **一键自动化安装脚本** - 支持主流 Linux 发行版的自动化部署
2. **详细的手动安装步骤** - 适用于需要自定义配置的场景
3. **全面的问题排查指南** - 覆盖常见的安装和运行问题
4. **生产环境配置建议** - 包括安全、性能和监控配置
5. **完整的官方文档链接** - 便于深入学习和参考

通过本指南，您可以快速在 Linux 服务器上部署一个功能完整的 Koishi 聊天机器人，并根据实际需求进行定制化配置。

如果在部署过程中遇到问题，建议：
1. 首先查看本指南的常见问题排查部分
2. 查阅官方文档获取最新信息
3. 在 GitHub 或社区论坛寻求帮助

祝您使用愉快！
