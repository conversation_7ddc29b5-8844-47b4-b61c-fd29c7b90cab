{"$schema": "https://biomejs.dev/schemas/2.1.1/schema.json", "root": true, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true, "defaultBranch": "main"}, "files": {"ignoreUnknown": false, "experimentalScannerIgnores": ["*.lock"], "includes": ["**"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSameLine": false, "bracketSpacing": true, "expand": "auto", "useEditorconfig": true}, "linter": {"enabled": true, "rules": {"recommended": true, "security": {"noGlobalEval": "error"}, "suspicious": {"noArrayIndexKey": "off", "noExplicitAny": "off", "noPrototypeBuiltins": "off", "noRedeclare": "off", "noVar": "warn"}, "correctness": {"noUnusedImports": "on"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "none", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSameLine": false, "indentStyle": "space", "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}, "linter": {"enabled": true}}, "json": {"parser": {"allowComments": true, "allowTrailingCommas": true}, "formatter": {"indentStyle": "space", "trailingCommas": "none", "expand": "always"}, "linter": {"enabled": true}}, "css": {"parser": {"allowWrongLineComments": null, "cssModules": true}, "formatter": {"enabled": true, "indentStyle": "space"}, "linter": {"enabled": true}, "assist": null}, "html": {"formatter": {"selfCloseVoidElements": "always"}}, "assist": {"enabled": null, "actions": {"source": {"organizeImports": "off"}}}}