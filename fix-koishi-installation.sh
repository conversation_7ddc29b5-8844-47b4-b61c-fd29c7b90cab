#!/bin/bash

# 修复 Koishi 安装脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

show_header() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi 安装修复工具"
    echo "========================================"
    echo -e "${NC}"
}

# 检查系统资源
check_system_resources() {
    log "INFO" "检查系统资源..."
    
    # 检查内存
    local total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    local free_mem=$(free -m | awk 'NR==2{printf "%.0f", $4}')
    
    log "INFO" "总内存: ${total_mem}MB, 可用内存: ${free_mem}MB"
    
    if [ "$free_mem" -lt 512 ]; then
        log "WARN" "可用内存不足，可能导致安装失败"
        log "FIX" "建议增加 swap 空间或使用 npm 替代 yarn"
    fi
    
    # 检查磁盘空间
    local disk_space=$(df -h . | awk 'NR==2 {print $4}')
    log "INFO" "可用磁盘空间: $disk_space"
}

# 增加 swap 空间
add_swap_space() {
    log "INFO" "检查 swap 空间..."
    
    local swap_size=$(free -m | awk 'NR==3{printf "%.0f", $2}')
    log "INFO" "当前 swap 空间: ${swap_size}MB"
    
    if [ "$swap_size" -lt 1024 ]; then
        read -p "是否创建 1GB swap 空间以改善安装? (y/N): " CREATE_SWAP
        
        if [[ $CREATE_SWAP == [yY] ]]; then
            log "FIX" "创建 swap 文件..."
            
            # 创建 1GB swap 文件
            dd if=/dev/zero of=/swapfile bs=1M count=1024 >/dev/null 2>&1
            chmod 600 /swapfile
            mkswap /swapfile >/dev/null 2>&1
            swapon /swapfile
            
            # 验证 swap
            local new_swap=$(free -m | awk 'NR==3{printf "%.0f", $2}')
            log "SUCCESS" "Swap 空间已增加到: ${new_swap}MB"
            
            # 添加到 fstab (可选)
            if ! grep -q "/swapfile" /etc/fstab; then
                echo "/swapfile none swap sw 0 0" >> /etc/fstab
                log "SUCCESS" "Swap 已添加到 fstab"
            fi
        fi
    fi
}

# 检查项目状态
check_project_status() {
    log "INFO" "检查项目状态..."
    
    if [ -d "koishi-app" ]; then
        log "SUCCESS" "找到项目目录: koishi-app"
        cd koishi-app
        
        # 检查 package.json
        if [ -f "package.json" ]; then
            log "SUCCESS" "找到 package.json"
            
            # 检查 node_modules
            if [ -d "node_modules" ]; then
                local modules_count=$(find node_modules -maxdepth 1 -type d | wc -l)
                log "INFO" "node_modules 目录存在，包含 $modules_count 个模块"
                
                if [ "$modules_count" -lt 10 ]; then
                    log "WARN" "依赖安装不完整，需要重新安装"
                    return 1
                else
                    log "SUCCESS" "依赖已安装"
                    return 0
                fi
            else
                log "WARN" "node_modules 目录不存在"
                return 1
            fi
        else
            log "ERROR" "package.json 不存在"
            return 1
        fi
    else
        log "ERROR" "项目目录不存在"
        return 1
    fi
}

# 清理并重新安装
clean_and_reinstall() {
    log "FIX" "清理并重新安装依赖..."
    
    if [ -d "koishi-app" ]; then
        cd koishi-app
    else
        log "ERROR" "项目目录不存在"
        return 1
    fi
    
    # 清理现有安装
    log "INFO" "清理现有文件..."
    rm -rf node_modules yarn.lock package-lock.json .yarn 2>/dev/null
    
    # 配置 npm 使用国内镜像
    log "FIX" "配置 npm 镜像..."
    npm config set registry https://registry.npmmirror.com
    npm config set disturl https://npmmirror.com/dist
    npm config set electron_mirror https://npmmirror.com/mirrors/electron/
    npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/
    npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/
    
    # 增加 npm 内存限制
    export NODE_OPTIONS="--max-old-space-size=2048"
    
    # 使用 npm 安装（更稳定）
    log "FIX" "使用 npm 安装依赖..."
    npm install --no-audit --no-fund --prefer-offline
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "依赖安装完成"
        return 0
    else
        log "ERROR" "npm 安装失败，尝试分批安装..."
        
        # 分批安装核心依赖
        log "FIX" "安装核心依赖..."
        npm install koishi @koishijs/plugin-console @koishijs/plugin-dataview --no-audit --no-fund
        
        if [ $? -eq 0 ]; then
            log "SUCCESS" "核心依赖安装完成"
            
            # 安装其他依赖
            log "FIX" "安装其他依赖..."
            npm install --no-audit --no-fund
            
            if [ $? -eq 0 ]; then
                log "SUCCESS" "所有依赖安装完成"
                return 0
            fi
        fi
        
        log "ERROR" "依赖安装失败"
        return 1
    fi
}

# 安装 pixluna 插件
install_pixluna_plugin() {
    log "INFO" "安装 pixluna 插件..."
    
    if [ ! -d "koishi-app" ]; then
        log "ERROR" "项目目录不存在"
        return 1
    fi
    
    cd koishi-app
    
    # 安装 pixluna
    log "FIX" "安装 koishi-plugin-pixluna..."
    npm install koishi-plugin-pixluna --no-audit --no-fund
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "pixluna 插件安装完成"
        
        # 添加配置
        add_pixluna_config
    else
        log "WARN" "pixluna 插件安装失败，可稍后手动安装"
    fi
}

# 添加 pixluna 配置
add_pixluna_config() {
    log "FIX" "配置 pixluna 插件..."
    
    # 检查配置文件
    if [ -f "koishi.yml" ]; then
        # 备份原配置
        cp koishi.yml koishi.yml.backup
        
        # 添加 pixluna 配置
        cat >> koishi.yml << 'EOF'

  # pixluna 插件配置 (已优化)
  pixluna:
    # 基础设置
    isR18: false
    r18P: 0.1
    excludeAI: true
    
    # 网络设置
    isProxy: false
    baseUrl: "i.pixiv.re"
    
    # 禁用自动撤回避免错误
    autoRecall:
      enable: false
      delay: 30
    
    # 启用压缩提高成功率
    imageProcessing:
      compress: true
      compressQuality: 0.7
      confusion: false
      isFlip: false
    
    # 消息设置
    forwardMessage: true
    messageBefore: "正在获取图片..."
    showTags: true
    
    # 启用日志便于调试
    isLog: true
    
    # 图片源设置 (多个备用源)
    defaultSourceProvider:
      - "lolicon"
      - "safebooru"
      - "gelbooru"
    
    # 并发控制
    maxConcurrency: 1
EOF
        
        log "SUCCESS" "pixluna 配置已添加"
    else
        log "WARN" "未找到 koishi.yml 配置文件"
    fi
}

# 构建项目
build_project() {
    log "INFO" "构建项目..."
    
    if [ ! -d "koishi-app" ]; then
        log "ERROR" "项目目录不存在"
        return 1
    fi
    
    cd koishi-app
    
    # 构建项目
    log "FIX" "执行构建..."
    npm run build
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "项目构建完成"
        return 0
    else
        log "WARN" "项目构建失败，但可能仍可运行"
        return 1
    fi
}

# 启动项目
start_project() {
    log "INFO" "准备启动项目..."
    
    if [ ! -d "koishi-app" ]; then
        log "ERROR" "项目目录不存在"
        return 1
    fi
    
    cd koishi-app
    
    read -p "是否立即启动 Koishi? (y/N): " START_NOW
    
    if [[ $START_NOW == [yY] ]]; then
        log "INFO" "启动 Koishi..."
        log "INFO" "访问地址: http://localhost:5140"
        log "INFO" "按 Ctrl+C 停止服务"
        echo
        
        npm start
    else
        log "INFO" "项目已准备就绪"
        echo
        echo -e "${GREEN}启动命令：${NC}"
        echo "cd koishi-app"
        echo "npm start"
        echo
        echo -e "${GREEN}访问地址：${NC}"
        echo "http://localhost:5140"
    fi
}

# 显示完成信息
show_completion_info() {
    echo
    log "SUCCESS" "Koishi 项目修复完成！"
    echo
    echo -e "${BLUE}项目信息：${NC}"
    echo "项目名称: koishi-app"
    echo "项目路径: $(pwd)/koishi-app"
    echo
    echo -e "${BLUE}常用命令：${NC}"
    echo "cd koishi-app"
    echo "npm start          # 启动项目"
    echo "npm run build      # 构建项目"
    echo "npm run dev        # 开发模式"
    echo
    echo -e "${BLUE}访问地址：${NC}"
    echo "控制台: http://localhost:5140"
    echo
    echo -e "${BLUE}测试命令：${NC}"
    echo "pixluna            # 获取随机图片"
    echo "pixluna 风景       # 获取风景图片"
    echo "help               # 查看帮助"
    echo
    echo -e "${GREEN}修复完成！${NC}"
}

# 主函数
main() {
    show_header
    
    log "INFO" "开始修复 Koishi 安装..."
    echo
    
    # 检查系统资源
    check_system_resources
    
    # 增加 swap 空间（如果需要）
    add_swap_space
    
    # 检查项目状态
    if ! check_project_status; then
        log "WARN" "项目安装不完整，开始修复..."
        
        if ! clean_and_reinstall; then
            log "ERROR" "依赖安装失败"
            exit 1
        fi
    fi
    
    # 安装 pixluna 插件
    install_pixluna_plugin
    
    # 构建项目
    build_project
    
    # 显示完成信息
    show_completion_info
    
    # 启动项目
    start_project
}

main "$@"
