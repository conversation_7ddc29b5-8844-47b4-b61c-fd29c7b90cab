# Koishi 插件开发完整指南

## 🎯 概述

本指南将带您从零开始创建、开发和发布 Koishi 插件。Koishi 是一个强大的跨平台聊天机器人框架，支持多种平台和丰富的插件生态。

## 📋 前置要求

### 环境准备
- **Node.js**: 版本 18+ (推荐 LTS 版本)
- **包管理器**: npm 或 yarn
- **编辑器**: VS Code (推荐，有更好的 TypeScript 支持)
- **基础知识**: JavaScript/TypeScript、Node.js 开发基础

### 安装 Node.js
1. 访问 [Node.js 官网](https://nodejs.org/)
2. 下载并安装 LTS 版本
3. 验证安装：
   ```bash
   node --version
   npm --version
   ```

### 配置包管理器 (可选)
```bash
# 安装 yarn (推荐)
npm install -g yarn

# 配置国内镜像 (中国用户)
npm config set registry https://registry.npmmirror.com
# 或
yarn config set registry https://registry.npmmirror.com
```

## 🚀 创建插件项目

### 方法1: 使用官方模板 (推荐)
```bash
# 创建新的 Koishi 项目
npm init koishi@latest my-koishi-bot

# 进入项目目录
cd my-koishi-bot

# 启动开发环境
npm start
```

### 方法2: 在现有项目中创建插件
```bash
# 在 Koishi 项目根目录下
npm run setup my-plugin-name
```

## 📁 项目结构

```
my-koishi-bot/
├── plugins/
│   └── my-plugin/              # 你的插件目录
│       ├── src/
│       │   ├── index.ts        # 插件主文件
│       │   └── locales/        # 国际化文件 (可选)
│       │       ├── zh.yml
│       │       └── en.yml
│       ├── package.json        # 插件包配置
│       └── tsconfig.json       # TypeScript 配置
├── koishi.yml                  # Koishi 配置文件
└── package.json                # 项目包配置
```

## 🔧 插件基本结构

### 最简单的插件示例

创建 `plugins/my-plugin/src/index.ts`：

```typescript
import { Context, Schema } from 'koishi'

export const name = 'my-plugin'

export interface Config {}

export const Config: Schema<Config> = Schema.object({})

export function apply(ctx: Context) {
  // 注册一个简单的命令
  ctx.command('hello', '打招呼')
    .action(({ session }) => {
      return `Hello, ${session?.username}!`
    })
}
```

### 带配置的插件示例

```typescript
import { Context, Schema } from 'koishi'

export const name = 'my-plugin'

export interface Config {
  prefix: string
  maxLength: number
  enableLogging: boolean
}

export const Config: Schema<Config> = Schema.object({
  prefix: Schema.string().default('Bot').description('回复前缀'),
  maxLength: Schema.number().min(1).max(1000).default(100).description('最大消息长度'),
  enableLogging: Schema.boolean().default(true).description('启用日志记录'),
})

export function apply(ctx: Context, config: Config) {
  const logger = ctx.logger('my-plugin')
  
  // 使用配置
  ctx.command('greet <name>', '个性化问候')
    .action(({ session }, name) => {
      if (config.enableLogging) {
        logger.info(`User ${session?.username} used greet command`)
      }
      
      const message = `${config.prefix}: Hello, ${name}!`
      
      if (message.length > config.maxLength) {
        return '消息太长了！'
      }
      
      return message
    })
}
```

## 📦 package.json 配置

创建 `plugins/my-plugin/package.json`：

```json
{
  "name": "koishi-plugin-my-plugin",
  "version": "1.0.0",
  "description": "我的第一个 Koishi 插件",
  "main": "lib/index.js",
  "typings": "lib/index.d.ts",
  "files": [
    "lib",
    "dist"
  ],
  "license": "MIT",
  "keywords": [
    "chatbot",
    "koishi",
    "plugin"
  ],
  "peerDependencies": {
    "koishi": "^4.15.0"
  },
  "koishi": {
    "description": {
      "en": "My first Koishi plugin",
      "zh": "我的第一个 Koishi 插件"
    },
    "service": {
      "required": [],
      "optional": [],
      "implements": []
    },
    "locales": ["en", "zh"]
  }
}
```

## 🎮 常用功能示例

### 1. 命令处理
```typescript
export function apply(ctx: Context) {
  // 基础命令
  ctx.command('ping', '测试连接')
    .action(() => 'Pong!')
  
  // 带参数的命令
  ctx.command('echo <message>', '回显消息')
    .action((_, message) => message)
  
  // 带选项的命令
  ctx.command('random', '生成随机数')
    .option('min', '-m <number> 最小值', { fallback: 1 })
    .option('max', '-M <number> 最大值', { fallback: 100 })
    .action(({ options }) => {
      const { min, max } = options
      return Math.floor(Math.random() * (max - min + 1)) + min
    })
}
```

### 2. 事件监听
```typescript
export function apply(ctx: Context) {
  // 监听消息事件
  ctx.on('message', (session) => {
    if (session.content === '你好') {
      session.send('你好！我是机器人')
    }
  })
  
  // 监听用户加入群组
  ctx.on('guild-member-added', (session) => {
    session.send(`欢迎 ${session.username} 加入群组！`)
  })
}
```

### 3. 中间件
```typescript
export function apply(ctx: Context) {
  // 消息预处理中间件
  ctx.middleware((session, next) => {
    // 记录所有消息
    console.log(`收到消息: ${session.content}`)
    
    // 过滤敏感词
    if (session.content.includes('敏感词')) {
      return '消息包含敏感词，已被过滤'
    }
    
    // 继续处理
    return next()
  })
}
```

### 4. 数据库操作
```typescript
import { Context, Schema } from 'koishi'

declare module 'koishi' {
  interface Tables {
    user_points: UserPoints
  }
}

export interface UserPoints {
  id: number
  userId: string
  points: number
}

export function apply(ctx: Context) {
  // 扩展数据库表
  ctx.model.extend('user_points', {
    id: 'unsigned',
    userId: 'string',
    points: 'integer',
  }, {
    primary: 'id',
    autoInc: true,
  })
  
  ctx.command('points', '查看积分')
    .action(async ({ session }) => {
      const [user] = await ctx.database.get('user_points', { userId: session.userId })
      return `你的积分: ${user?.points || 0}`
    })
  
  ctx.command('add-points <amount:number>', '增加积分')
    .action(async ({ session }, amount) => {
      await ctx.database.upsert('user_points', [
        { userId: session.userId, points: amount }
      ])
      return `已增加 ${amount} 积分`
    })
}
```

## 🌍 国际化支持

### 创建语言文件

`plugins/my-plugin/src/locales/zh.yml`:
```yaml
commands:
  hello:
    description: 打招呼
    messages:
      greeting: 你好，{0}！

errors:
  invalid-input: 输入无效
```

`plugins/my-plugin/src/locales/en.yml`:
```yaml
commands:
  hello:
    description: Say hello
    messages:
      greeting: Hello, {0}!

errors:
  invalid-input: Invalid input
```

### 在代码中使用
```typescript
export function apply(ctx: Context) {
  ctx.i18n.define('zh', require('./locales/zh'))
  ctx.i18n.define('en', require('./locales/en'))
  
  ctx.command('hello', 'commands.hello.description')
    .action(({ session }) => {
      return session.text('commands.hello.messages.greeting', session.username)
    })
}
```

## 🧪 测试和调试

### 本地测试
```bash
# 在项目根目录启动开发服务器
npm start

# 或使用 yarn
yarn start
```

### 构建插件
```bash
# 构建所有插件
npm run build

# 构建特定插件
npm run build my-plugin
```

## 📤 发布插件

### 1. 更新版本号
```bash
# 更新补丁版本 (1.0.0 -> 1.0.1)
npm run bump my-plugin

# 更新次要版本 (1.0.0 -> 1.1.0)
npm run bump my-plugin -- --minor

# 更新主要版本 (1.0.0 -> 2.0.0)
npm run bump my-plugin -- --major
```

### 2. 发布到 npm
```bash
# 发布插件
npm run pub my-plugin

# 如果在中国，使用官方镜像发布
npm run pub my-plugin -- --registry https://registry.npmjs.org
```

### 3. 发布前检查清单
- ✅ 插件名称符合规范 (`koishi-plugin-*`)
- ✅ 版本号遵循语义化版本
- ✅ `package.json` 包含必要字段
- ✅ 代码已构建且无错误
- ✅ 功能测试通过
- ✅ 文档完整

## 🎯 最佳实践

### 1. 代码规范
- 使用 TypeScript 获得更好的类型安全
- 遵循 ESLint 规则
- 添加适当的注释和文档
- 使用有意义的变量和函数名

### 2. 错误处理
```typescript
export function apply(ctx: Context) {
  ctx.command('risky-operation')
    .action(async ({ session }) => {
      try {
        // 可能出错的操作
        const result = await someRiskyOperation()
        return `操作成功: ${result}`
      } catch (error) {
        ctx.logger('my-plugin').error('操作失败:', error)
        return '操作失败，请稍后重试'
      }
    })
}
```

### 3. 性能优化
- 避免阻塞操作
- 使用适当的缓存
- 限制资源使用
- 优雅处理大量数据

### 4. 安全考虑
- 验证用户输入
- 避免 SQL 注入
- 限制权限和访问
- 不暴露敏感信息

## 📚 进阶功能

### 服务注册
```typescript
declare module 'koishi' {
  interface Context {
    myService: MyService
  }
}

class MyService {
  constructor(private ctx: Context) {}
  
  async doSomething() {
    // 服务逻辑
  }
}

export function apply(ctx: Context) {
  ctx.plugin(MyService)
}
```

### 控制台扩展
```typescript
export function apply(ctx: Context) {
  // 添加控制台页面
  ctx.console.addEntry({
    dev: resolve(__dirname, '../client/index.ts'),
    prod: resolve(__dirname, '../dist'),
  })
}
```

## 🔗 有用的资源

- [Koishi 官方文档](https://koishi.chat/)
- [插件市场](https://koishi.chat/market/)
- [GitHub 仓库](https://github.com/koishijs/koishi)
- [社区讨论](https://github.com/koishijs/koishi/discussions)
- [官方插件示例](https://github.com/koishijs)

## 🎉 总结

现在您已经掌握了 Koishi 插件开发的基础知识！从简单的命令处理到复杂的数据库操作，从本地测试到发布上线，这个指南涵盖了插件开发的完整流程。

开始创建您的第一个 Koishi 插件吧！
