#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证版QQ机器人
基于成功的认证测试
"""

import asyncio
import logging
import traceback
from datetime import datetime

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class VerifiedBot(botpy.Client):
    """验证版QQ机器人"""
    
    def __init__(self):
        # 设置事件订阅
        intents = botpy.Intents(
            public_guild_messages=True,  # 公域消息事件
            direct_message=True,  # 私信事件
        )
        
        super().__init__(intents=intents)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='[%(levelname)s] %(asctime)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/bot.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)

    async def on_ready(self):
        """机器人启动完成事件"""
        self.logger.info(f"机器人 {self.robot.name} 已上线！")
        self.logger.info(f"机器人ID: {self.robot.id}")
        print("=" * 50)
        print(f"🎉 机器人启动成功！")
        print(f"🤖 机器人名称: {self.robot.name}")
        print(f"🆔 机器人ID: {self.robot.id}")
        print(f"📱 机器人QQ号: 3889706980")
        print("=" * 50)
        print("✅ 机器人已准备就绪，等待消息...")
        print("💡 在QQ频道中@机器人发送消息进行测试")
        print("=" * 50)

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息"""
        try:
            self.logger.info(f"收到@消息: {message.content} (来自: {message.author.username})")
            print(f"📨 收到@消息: {message.content}")
            
            # 提取消息内容
            content = self.extract_content(message.content)
            print(f"💭 处理内容: {content}")
            
            # 处理不同类型的消息
            if content.startswith('/'):
                await self.handle_command(message, content)
            else:
                await self.handle_chat(message, content)
                
        except Exception as e:
            self.logger.error(f"处理@消息时发生错误: {e}")
            self.logger.error(traceback.format_exc())
            print(f"❌ 处理消息时发生错误: {e}")
            await self.send_error_message(message)

    async def on_direct_message_create(self, message: Message):
        """处理私信消息"""
        try:
            self.logger.info(f"收到私信: {message.content} (来自: {message.author.username})")
            print(f"💬 收到私信: {message.content}")
            
            content = message.content.strip()
            
            if content.startswith('/'):
                await self.handle_command(message, content)
            else:
                await self.handle_chat(message, content)
                
        except Exception as e:
            self.logger.error(f"处理私信时发生错误: {e}")
            print(f"❌ 处理私信时发生错误: {e}")
            await self.send_error_message(message)

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容，去除@机器人部分"""
        import re
        # 使用正则表达式去除@机器人的部分
        content = re.sub(r'<@!\d+>\s*', '', raw_content).strip()
        return content

    async def handle_command(self, message: Message, content: str):
        """处理命令"""
        parts = content.split()
        command = parts[0][1:].lower()  # 去除/前缀
        args = parts[1:] if len(parts) > 1 else []
        
        self.logger.info(f"执行命令: {command}, 参数: {args}")
        print(f"⚡ 执行命令: /{command}")
        
        # 命令路由
        if command == "help":
            await self.cmd_help(message)
        elif command == "ping":
            await self.cmd_ping(message)
        elif command == "time":
            await self.cmd_time(message)
        elif command == "hello":
            await self.cmd_hello(message)
        elif command == "info":
            await self.cmd_info(message)
        elif command == "test":
            await self.cmd_test(message)
        else:
            await self.send_message(message, f"❓ 未知命令: /{command}\n输入 /help 查看可用命令")

    async def handle_chat(self, message: Message, content: str):
        """处理普通聊天"""
        if not content:
            return

        print(f"💭 处理聊天: {content}")

        # 随机回复功能
        import random
        content_lower = content.lower()

        if any(word in content_lower for word in ["你好", "hello", "hi", "嗨"]):
            hello_replies = [
                "你好！很高兴见到你！😊\n我是QQ机器人，输入 /help 查看我的功能",
                "嗨！今天过得怎么样？🌟\n有什么我可以帮助你的吗？",
                "你好呀！欢迎来聊天！😄\n试试输入 /help 看看我能做什么",
                "Hello！很开心认识你！👋\n我是你的专属QQ机器人！",
                "你好！我正在等你呢！🤖\n输入 /test 测试我的功能吧！",
                "嗨嗨！今天天气不错呢～☀️\n要不要聊聊天？",
                "你好！感谢你和我打招呼！💕\n我们开始愉快的对话吧！",
                "Hello World！程序员的经典问候！💻\n我是用Python写的哦！",
                "你好！准备好开始有趣的聊天了吗？😊✨",
                "嗨！我刚刚在想你什么时候会来呢！💭",
                "你好！今天是美好的一天！🌈\n让我们一起度过愉快的时光！",
                "Hello！很高兴在这里遇见你！🎉\n有什么想聊的吗？"
            ]
            reply = random.choice(hello_replies)
        elif any(word in content_lower for word in ["再见", "bye", "拜拜"]):
            bye_replies = [
                "再见！期待下次聊天！👋",
                "拜拜！记得想我哦～😘",
                "再见！愿你每天都开心！🌟",
                "Bye bye！下次见！✨",
                "再见！保重身体！💪",
                "拜拜！期待我们的下次相遇！🤗",
                "再见！今天聊得很开心！😊",
                "拜拜！记得常来找我玩！🎮"
            ]
            reply = random.choice(bye_replies)
        elif any(word in content_lower for word in ["谢谢", "thank", "感谢"]):
            thank_replies = [
                "不客气！很高兴能帮到你！😄",
                "不用谢！这是我应该做的！😊",
                "客气什么！我们是朋友嘛！🤝",
                "不客气！有需要随时找我！💪",
                "谢谢你的感谢！😄",
                "能帮到你我很开心！🌟",
                "不用客气！互相帮助嘛！🤗"
            ]
            reply = random.choice(thank_replies)
        elif "机器人" in content_lower or "bot" in content_lower:
            bot_replies = [
                "是的，我是QQ机器人！🤖\n我可以聊天和执行命令，输入 /help 了解更多！",
                "没错！我是你的智能助手！🤖✨\n有什么需要帮助的吗？",
                "对呀！我是用Python开发的QQ机器人！💻\n很高兴为你服务！"
            ]
            reply = random.choice(bot_replies)
        elif any(word in content_lower for word in ["时间", "几点"]):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            time_replies = [
                f"🕐 现在是: {current_time}",
                f"⏰ 当前时间: {current_time}\n时间过得真快呢！",
                f"🕐 时间显示: {current_time}\n要珍惜时间哦！"
            ]
            reply = random.choice(time_replies)
        elif any(word in content_lower for word in ["测试", "test"]):
            test_replies = [
                "✅ 机器人工作正常！\n输入 /test 进行更详细的测试",
                "🧪 测试中...一切正常！\n我准备好为你服务了！",
                "✅ 系统状态良好！\n所有功能都在正常运行！"
            ]
            reply = random.choice(test_replies)
        else:
            default_replies = [
                "我收到了你的消息！😊\n输入 /help 查看我能做什么～",
                "有趣！告诉我更多吧！😊\n我很喜欢和你聊天！",
                "我在认真听呢！继续说～👂\n你的话题很有意思！",
                "这个话题很有意思！💭\n我想了解更多！",
                "我正在学习中，谢谢你的分享！📚\n每次聊天我都能学到新东西！",
                "哇，真的吗？听起来很棒！✨\n继续和我分享吧！"
            ]
            reply = random.choice(default_replies)

        await self.send_message(message, reply)

    # 命令处理函数
    async def cmd_help(self, message: Message):
        """帮助命令"""
        help_text = """🤖 QQ机器人帮助信息

📝 可用命令：
/help - 显示此帮助信息
/ping - 测试机器人响应
/time - 获取当前时间
/hello - 打招呼
/info - 查看机器人信息
/test - 运行测试

💬 聊天功能：
直接@我发送消息即可聊天

🎯 关键词：
- 发送"你好"、"时间"、"测试"等关键词试试看

❓ 如有问题，请联系管理员"""
        
        await self.send_message(message, help_text)

    async def cmd_ping(self, message: Message):
        """Ping命令"""
        await self.send_message(message, "🏓 Pong! 机器人运行正常！")

    async def cmd_time(self, message: Message):
        """时间命令"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        await self.send_message(message, f"🕐 当前时间: {current_time}")

    async def cmd_hello(self, message: Message):
        """打招呼命令"""
        username = message.author.username if message.author else "朋友"
        await self.send_message(message, f"👋 你好 {username}！我是QQ机器人，很高兴认识你！")

    async def cmd_info(self, message: Message):
        """机器人信息"""
        info_text = f"""🤖 机器人信息

🏷️ 名称: {self.robot.name}
🆔 ID: {self.robot.id}
📱 AppID: {BOT_CONFIG['appid']}
📞 QQ号: 3889706980
⚡ 状态: 运行中
🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 这是一个基于QQ开放平台的机器人
✅ 认证状态: 已验证"""
        
        await self.send_message(message, info_text)

    async def cmd_test(self, message: Message):
        """测试命令"""
        test_text = """🧪 机器人测试报告

✅ 消息接收: 正常
✅ 消息发送: 正常
✅ 命令解析: 正常
✅ 日志记录: 正常
✅ 错误处理: 正常

🎯 测试建议:
1. 尝试发送不同的消息
2. 测试各种命令
3. 发送关键词触发智能回复

📊 系统信息:
- Python版本: 3.10+
- botpy SDK: 已安装
- 配置状态: 已验证"""
        
        await self.send_message(message, test_text)

    async def send_message(self, original_message: Message, content: str):
        """发送消息"""
        try:
            # 发送消息到频道
            await self.api.post_message(
                channel_id=original_message.channel_id,
                content=content,
                msg_id=original_message.id
            )
            self.logger.info(f"发送消息成功: {content[:50]}...")
            print(f"✅ 消息发送成功")
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            print(f"❌ 发送消息失败: {e}")

    async def send_error_message(self, message: Message):
        """发送错误消息"""
        await self.send_message(message, "❌ 处理消息时发生错误，请稍后重试")


def main():
    """主函数"""
    print("🚀 正在启动验证版QQ机器人...")
    print("=" * 50)
    print(f"📱 AppID: {BOT_CONFIG['appid']}")
    print(f"🔑 使用已验证的AppSecret")
    print("=" * 50)
    
    # 创建机器人实例
    bot = VerifiedBot()
    
    try:
        # 使用验证成功的AppSecret启动
        print("🔄 使用AppSecret启动机器人...")
        bot.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 详细错误信息:")
        traceback.print_exc()
        
        print("\n💡 如果遇到问题，请检查：")
        print("1. 网络连接是否正常")
        print("2. 机器人是否已在QQ开放平台配置沙箱环境")
        print("3. 是否需要配置IP白名单")


if __name__ == "__main__":
    main()
