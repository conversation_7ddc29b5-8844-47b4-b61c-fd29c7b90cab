// 修复后的 request.ts 文件
import type { Context } from 'koishi'
import type { GeneralImageData, SourceProvider } from './type'
import { taskTime } from './taskManager'
import { processImage } from './imageProcessing'
import { getProvider } from '../providers'
import { logger } from '../index'
import { detectMimeType } from './mimeUtils'
import type {} from '@koishijs/plugin-proxy-agent'
import type Config from '../config'

export const USER_AGENT =
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36'

// 添加重试机制和更好的错误处理
export async function fetchImageBuffer(
  ctx: Context,
  config: Config,
  url: string,
  provider?: SourceProvider,
  retries: number = 3
): Promise<[ArrayBuffer, string]> {
  return taskTime(ctx, 'fetchImage', async () => {
    const headers: Record<string, string> = {
      'User-Agent': USER_AGENT
    }

    if (provider?.getMeta?.()?.referer) {
      headers.Referer = provider.getMeta().referer
    }

    let lastError: Error
    
    // 重试机制
    for (let i = 0; i < retries; i++) {
      try {
        logger.debug(`尝试获取图片 (${i + 1}/${retries})`, { url })
        
        const response = await ctx.http.get(url, {
          responseType: 'arraybuffer',
          proxyAgent: config.isProxy ? config.proxyHost : undefined,
          headers,
          timeout: 30000, // 增加超时时间
        })

        const mimeType = detectMimeType(response)
        logger.debug('检测到MIME类型', { mimeType })

        return [response, mimeType]
      } catch (error) {
        lastError = error
        logger.warn(`图片获取失败 (${i + 1}/${retries})`, { 
          url, 
          error: error.message,
          status: error.response?.status 
        })
        
        // 如果是 404 或其他客户端错误，不重试
        if (error.response?.status >= 400 && error.response?.status < 500) {
          break
        }
        
        // 等待后重试
        if (i < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
        }
      }
    }
    
    throw new Error(`图片获取失败: ${lastError?.message || '未知错误'}`)
  })
}

export async function getRemoteImage(
  ctx: Context,
  tag: string,
  config: Config,
  specificProvider?: string
): Promise<
  GeneralImageData & {
    data: Buffer
    mimeType: string
    raw: GeneralImageData
  }
> {
  // 验证 provider 配置
  let provider: SourceProvider
  try {
    provider = getProvider(ctx, config, specificProvider)
    if (!provider) {
      throw new Error('未选择有效的图片来源，请检查配置')
    }
  } catch (error) {
    logger.error('获取图片源失败', { error: error.message, specificProvider })
    throw new Error(`图片源配置错误: ${error.message}`)
  }

  // 获取元数据，添加重试机制
  let metadata
  const maxRetries = 3
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      logger.debug(`尝试获取元数据 (${i + 1}/${maxRetries})`, { 
        provider: specificProvider || config.defaultSourceProvider[0],
        tag 
      })
      
      metadata = await provider.getMetaData(
        { context: ctx },
        {
          r18: config.isR18 && Math.random() < config.r18P,
          excludeAI: config.excludeAI,
          tag: tag ? tag.split(' ').join('|') : void 0,
          proxy: config.baseUrl ? config.baseUrl : void 0
        }
      )
      
      if (metadata.status === 'success') {
        break
      } else {
        logger.warn(`元数据获取失败 (${i + 1}/${maxRetries})`, { 
          error: metadata.data?.message 
        })
      }
    } catch (error) {
      logger.warn(`元数据获取异常 (${i + 1}/${maxRetries})`, { 
        error: error.message 
      })
      
      if (i === maxRetries - 1) {
        throw new Error(`元数据获取失败: ${error.message}`)
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }

  if (metadata.status === 'error') {
    throw new Error(`无法获取图片元数据: ${metadata.data?.message || '未知错误'}`)
  }

  if (!metadata.data?.url) {
    throw new Error('获取到的图片URL无效')
  }

  // 获取图片数据
  const [buffer, mimeType] = await fetchImageBuffer(
    ctx,
    config,
    metadata.data.url,
    provider
  )

  const data = await taskTime(ctx, 'processImage', async () => {
    const imageBuffer = Buffer.from(buffer)
    return await processImage(
      ctx,
      imageBuffer,
      config,
      !!metadata.data.urls?.regular
    )
  })

  return {
    ...metadata.data.raw,
    data,
    mimeType,
    raw: metadata.data.raw
  }
}
