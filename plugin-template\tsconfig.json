{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "declaration": true, "outDir": "lib", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "lib", "dist"]}