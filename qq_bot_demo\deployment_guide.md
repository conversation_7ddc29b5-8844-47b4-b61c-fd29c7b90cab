# QQ机器人部署和测试指南

## 部署前准备

### 1. 开发者平台配置

#### 1.1 沙箱环境配置
在QQ开放平台管理后台：

1. **配置沙箱频道**
   - 进入"沙箱配置"页面
   - 添加测试频道（你必须是频道主或管理员）
   - 频道成员不能超过20人

2. **配置开发基础设置**
   - 记录AppID、Token、AppSecret
   - 配置IP白名单（如果启用）
   - 设置回调地址（如需要）

#### 1.2 机器人权限设置
确保机器人具有以下权限：
- 发送消息
- 接收消息
- 管理消息（可选）

### 2. 本地开发环境

#### 2.1 Python环境
```bash
# 检查Python版本（需要3.8+）
python --version

# 创建虚拟环境（推荐）
python -m venv qq_bot_env

# 激活虚拟环境
# Windows:
qq_bot_env\Scripts\activate
# Linux/Mac:
source qq_bot_env/bin/activate
```

#### 2.2 安装依赖
```bash
# 安装项目依赖
pip install -r requirements.txt

# 或者单独安装
pip install qq-botpy
```

## 配置机器人

### 1. 修改配置文件

编辑 `config.py` 文件：

```python
BOT_CONFIG = {
    "appid": "你的机器人AppID",      # 替换为实际AppID
    "token": "你的机器人Token",      # 替换为实际Token
    "secret": "你的机器人AppSecret", # 替换为实际AppSecret
}
```

### 2. 测试配置

创建一个简单的测试脚本 `test_config.py`：

```python
from config import BOT_CONFIG

def test_config():
    required_fields = ["appid", "token", "secret"]
    
    for field in required_fields:
        value = BOT_CONFIG.get(field)
        if not value or value.startswith("你的"):
            print(f"❌ {field} 未正确配置")
            return False
        else:
            print(f"✅ {field} 已配置")
    
    print("✅ 配置检查通过！")
    return True

if __name__ == "__main__":
    test_config()
```

## 本地测试

### 1. 启动机器人

```bash
# 基础版本
python bot.py

# 高级版本
python advanced_example.py

# 调试模式
python bot.py -d
```

### 2. 测试步骤

#### 2.1 添加机器人到测试频道

1. 在QQ开放平台管理后台找到机器人二维码
2. 用手机QQ扫码
3. 选择添加到配置的沙箱频道

#### 2.2 基础功能测试

在频道中测试以下功能：

1. **@机器人测试**
   ```
   @机器人名称 你好
   @机器人名称 /help
   @机器人名称 /ping
   ```

2. **命令测试**
   ```
   @机器人名称 /time
   @机器人名称 /weather 北京
   @机器人名称 /help
   ```

3. **私信测试**
   - 点击机器人头像
   - 发送私信消息
   - 测试私信命令

#### 2.3 高级功能测试（advanced_example.py）

```
@机器人名称 /remind 1 测试提醒
@机器人名称 /random 1 100
@机器人名称 /calc 2+3*4
@机器人名称 /joke
@机器人名称 /userinfo
```

### 3. 日志检查

检查 `logs/bot.log` 文件：
- 确认机器人启动成功
- 查看消息接收和处理日志
- 检查是否有错误信息

## 生产环境部署

### 1. 服务器环境准备

#### 1.1 Linux服务器（推荐）

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python3和pip
sudo apt install python3 python3-pip python3-venv -y

# 安装Git
sudo apt install git -y
```

#### 1.2 创建部署目录

```bash
# 创建应用目录
sudo mkdir -p /opt/qq_bot
sudo chown $USER:$USER /opt/qq_bot

# 上传代码
cd /opt/qq_bot
git clone <你的代码仓库> .
# 或者直接上传文件
```

### 2. 生产环境配置

#### 2.1 环境变量配置

创建 `.env` 文件：

```bash
# .env
QQ_BOT_APPID=你的AppID
QQ_BOT_TOKEN=你的Token
QQ_BOT_SECRET=你的AppSecret
```

修改 `config.py` 支持环境变量：

```python
import os
from dotenv import load_dotenv

load_dotenv()

BOT_CONFIG = {
    "appid": os.getenv("QQ_BOT_APPID", ""),
    "token": os.getenv("QQ_BOT_TOKEN", ""),
    "secret": os.getenv("QQ_BOT_SECRET", ""),
}
```

#### 2.2 安装依赖

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
pip install python-dotenv  # 用于环境变量
```

### 3. 进程管理

#### 3.1 使用systemd（推荐）

创建服务文件 `/etc/systemd/system/qq-bot.service`：

```ini
[Unit]
Description=QQ Bot Service
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/opt/qq_bot
Environment=PATH=/opt/qq_bot/venv/bin
ExecStart=/opt/qq_bot/venv/bin/python bot.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start qq-bot

# 设置开机自启
sudo systemctl enable qq-bot

# 查看状态
sudo systemctl status qq-bot

# 查看日志
sudo journalctl -u qq-bot -f
```

#### 3.2 使用PM2（Node.js环境）

```bash
# 安装PM2
npm install -g pm2

# 创建PM2配置文件 ecosystem.config.js
module.exports = {
  apps: [{
    name: 'qq-bot',
    script: 'python',
    args: 'bot.py',
    cwd: '/opt/qq_bot',
    interpreter: '/opt/qq_bot/venv/bin/python',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
};

# 启动
pm2 start ecosystem.config.js

# 保存配置
pm2 save

# 设置开机自启
pm2 startup
```

### 4. 监控和维护

#### 4.1 日志轮转

创建 `/etc/logrotate.d/qq-bot`：

```
/opt/qq_bot/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        systemctl reload qq-bot
    endscript
}
```

#### 4.2 监控脚本

创建健康检查脚本 `health_check.py`：

```python
#!/usr/bin/env python3
import requests
import time
import subprocess

def check_bot_health():
    try:
        # 检查进程是否运行
        result = subprocess.run(['pgrep', '-f', 'bot.py'], 
                              capture_output=True, text=True)
        if not result.stdout.strip():
            print("❌ 机器人进程未运行")
            return False
        
        print("✅ 机器人进程正常")
        return True
        
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

if __name__ == "__main__":
    if not check_bot_health():
        # 重启服务
        subprocess.run(['sudo', 'systemctl', 'restart', 'qq-bot'])
        print("🔄 已尝试重启服务")
```

设置定时检查：

```bash
# 添加到crontab
crontab -e

# 每5分钟检查一次
*/5 * * * * /opt/qq_bot/venv/bin/python /opt/qq_bot/health_check.py
```

## 常见问题排查

### 1. 连接问题

**问题**: 机器人无法连接到QQ服务器

**解决方案**:
- 检查网络连接
- 验证AppID和Token是否正确
- 检查IP白名单配置
- 查看防火墙设置

### 2. 权限问题

**问题**: 机器人无法发送消息

**解决方案**:
- 确认机器人已添加到频道/群
- 检查机器人权限设置
- 验证频道/群的机器人权限

### 3. 消息接收问题

**问题**: 机器人收不到消息

**解决方案**:
- 检查事件订阅配置（Intents）
- 确认消息格式正确
- 查看日志中的错误信息

### 4. 性能问题

**问题**: 机器人响应慢或内存占用高

**解决方案**:
- 优化代码逻辑
- 增加异步处理
- 监控资源使用情况
- 考虑使用缓存

## 安全建议

1. **敏感信息保护**
   - 使用环境变量存储密钥
   - 不要将密钥提交到代码仓库
   - 定期更换Token

2. **访问控制**
   - 配置IP白名单
   - 限制机器人权限
   - 监控异常访问

3. **代码安全**
   - 验证用户输入
   - 防止代码注入
   - 限制命令执行权限

4. **数据安全**
   - 加密敏感数据
   - 定期备份
   - 遵守数据保护法规
