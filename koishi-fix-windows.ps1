# Koishi Windows 修复脚本
# 用于修复 Koishi 在 Windows 环境下的访问问题

param(
    [string]$ProjectPath = ".",
    [int]$Port = 5140
)

# 颜色定义
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Cyan"
    NC = "White"
}

function Write-Log {
    param(
        [string]$Level,
        [string]$Message
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "INFO" { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor $Colors.Blue }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor $Colors.Green }
        "WARN" { Write-Host "[$timestamp] [WARN] $Message" -ForegroundColor $Colors.Yellow }
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor $Colors.Red }
        "FIX" { Write-Host "[$timestamp] [FIX] $Message" -ForegroundColor $Colors.Yellow }
    }
}

function Show-Header {
    Write-Host ""
    Write-Host "=================================" -ForegroundColor $Colors.Green
    Write-Host "    Koishi Windows 修复工具" -ForegroundColor $Colors.Green
    Write-Host "=================================" -ForegroundColor $Colors.Green
    Write-Host ""
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    } catch {
        return $false
    }
}

function Stop-ExistingProcesses {
    Write-Log "INFO" "步骤1：停止现有的 Koishi 进程..."
    
    # 查找并停止 Node.js 进程
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($nodeProcesses) {
        Write-Log "FIX" "发现 $($nodeProcesses.Count) 个 Node.js 进程，正在停止..."
        $nodeProcesses | Stop-Process -Force
        Write-Log "SUCCESS" "Node.js 进程已停止"
    } else {
        Write-Log "INFO" "没有发现运行中的 Node.js 进程"
    }
    
    # 检查端口占用
    $portProcess = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portProcess) {
        $pid = $portProcess.OwningProcess
        Write-Log "FIX" "端口 $Port 被进程 $pid 占用，正在停止..."
        Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue
        Write-Log "SUCCESS" "端口 $Port 已释放"
    } else {
        Write-Log "SUCCESS" "端口 $Port 未被占用"
    }
}

function Install-Tools {
    Write-Log "INFO" "步骤2：检查必要工具..."
    
    $toolsNeeded = @()
    
    if (!(Test-Command "node")) {
        $toolsNeeded += "Node.js"
    }
    
    if (!(Test-Command "npm")) {
        $toolsNeeded += "npm"
    }
    
    if ($toolsNeeded.Count -gt 0) {
        Write-Log "WARN" "缺少工具: $($toolsNeeded -join ', ')"
        Write-Log "INFO" "请手动安装 Node.js: https://nodejs.org/"
        return $false
    } else {
        Write-Log "SUCCESS" "所需工具已安装"
        
        # 显示版本信息
        $nodeVersion = node --version
        $npmVersion = npm --version
        Write-Log "INFO" "Node.js 版本: $nodeVersion"
        Write-Log "INFO" "npm 版本: $npmVersion"
        return $true
    }
}

function Reinstall-NpmPackages {
    Write-Log "INFO" "步骤2.5：检查 npm 包..."
    
    $packageJsonPath = Join-Path $ProjectPath "package.json"
    if (!(Test-Path $packageJsonPath)) {
        Write-Log "WARN" "未找到 package.json，跳过 npm 包检查"
        return $true
    }
    
    $nodeModulesPath = Join-Path $ProjectPath "node_modules"
    if (!(Test-Path $nodeModulesPath)) {
        Write-Log "FIX" "未找到 node_modules，正在安装..."
        Set-Location $ProjectPath
        npm install
        if ($LASTEXITCODE -eq 0) {
            Write-Log "SUCCESS" "npm 包安装完成"
        } else {
            Write-Log "WARN" "npm 包安装可能有问题"
        }
    } else {
        Write-Log "SUCCESS" "node_modules 已存在"
    }
    
    return $true
}

function Fix-ConfigFile {
    Write-Log "INFO" "步骤3：修复 Koishi 配置文件..."
    
    $configPath = Join-Path $ProjectPath "koishi.yml"
    if (!(Test-Path $configPath)) {
        $configPath = Join-Path $ProjectPath "koishi.config.js"
        if (!(Test-Path $configPath)) {
            Write-Log "WARN" "未找到配置文件，将创建默认配置"
            $defaultConfig = @"
port: $Port
host: 0.0.0.0
plugins:
  console:
    open: true
"@
            $defaultConfig | Out-File -FilePath (Join-Path $ProjectPath "koishi.yml") -Encoding UTF8
            Write-Log "SUCCESS" "已创建默认配置文件"
            return $true
        }
    }
    
    Write-Log "SUCCESS" "配置文件检查完成"
    return $true
}

function Configure-Firewall {
    Write-Log "INFO" "步骤4：配置 Windows 防火墙..."
    
    try {
        # 检查防火墙规则是否存在
        $existingRule = Get-NetFirewallRule -DisplayName "Koishi-$Port" -ErrorAction SilentlyContinue
        if ($existingRule) {
            Write-Log "SUCCESS" "防火墙规则已存在"
        } else {
            Write-Log "FIX" "添加防火墙规则..."
            New-NetFirewallRule -DisplayName "Koishi-$Port" -Direction Inbound -Protocol TCP -LocalPort $Port -Action Allow | Out-Null
            Write-Log "SUCCESS" "防火墙规则已添加"
        }
    } catch {
        Write-Log "WARN" "无法配置防火墙规则，可能需要管理员权限"
    }
}

function Start-Koishi {
    Write-Log "INFO" "步骤5：启动 Koishi..."
    
    Set-Location $ProjectPath
    
    if (!(Test-Path "package.json")) {
        Write-Log "ERROR" "未找到 package.json，请确保在正确的项目目录中"
        return $false
    }
    
    Write-Log "FIX" "启动 Koishi 服务..."
    
    # 使用 Start-Process 在后台启动
    $process = Start-Process -FilePath "npm" -ArgumentList "start" -PassThru -WindowStyle Hidden
    
    if ($process) {
        Write-Log "SUCCESS" "Koishi 启动命令已执行 (PID: $($process.Id))"
        
        # 等待几秒钟让服务启动
        Start-Sleep -Seconds 5
        
        return $true
    } else {
        Write-Log "ERROR" "Koishi 启动失败"
        return $false
    }
}

function Test-Access {
    Write-Log "INFO" "步骤6：验证访问..."
    
    $maxAttempts = 10
    $attempt = 0
    
    while ($attempt -lt $maxAttempts) {
        $attempt++
        Write-Log "INFO" "尝试访问 ($attempt/$maxAttempts)..."
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 5 -ErrorAction Stop
            if ($response.StatusCode -eq 200) {
                Write-Log "SUCCESS" "Koishi 服务访问正常！"
                return $true
            }
        } catch {
            Write-Log "WARN" "访问失败: $($_.Exception.Message)"
        }
        
        if ($attempt -lt $maxAttempts) {
            Start-Sleep -Seconds 3
        }
    }
    
    Write-Log "ERROR" "无法访问 Koishi 服务"
    return $false
}

function Show-AccessInfo {
    Write-Host ""
    Write-Host "🎉 Koishi 修复完成！" -ForegroundColor $Colors.Green
    Write-Host ""
    Write-Host "访问地址：" -ForegroundColor $Colors.Blue
    Write-Host "  本地访问: http://localhost:$Port"
    Write-Host "  内网访问: http://127.0.0.1:$Port"
    
    # 获取本机 IP
    $localIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -ne "127.0.0.1" -and $_.PrefixOrigin -eq "Dhcp" }).IPAddress
    if ($localIP) {
        Write-Host "  局域网访问: http://$localIP:$Port"
    }
    
    Write-Host ""
    Write-Host "管理命令：" -ForegroundColor $Colors.Blue
    Write-Host "  查看进程: Get-Process -Name node"
    Write-Host "  停止服务: Stop-Process -Name node"
    Write-Host ""
    Write-Host "注意事项：" -ForegroundColor $Colors.Yellow
    Write-Host "• 首次访问可能需要几分钟初始化"
    Write-Host "• 如果仍无法访问，请检查防火墙设置"
    Write-Host "• 确保 Node.js 和 npm 已正确安装"
    Write-Host ""
}

# 主函数
function Main {
    Show-Header
    
    Write-Log "INFO" "开始修复 Koishi 访问问题..."
    Write-Host ""
    
    # 解析项目路径
    $ProjectPath = Resolve-Path $ProjectPath
    Write-Log "INFO" "项目路径: $ProjectPath"
    Write-Host ""
    
    # 执行各个步骤
    Stop-ExistingProcesses
    Write-Host ""
    
    if (!(Install-Tools)) {
        Write-Log "ERROR" "工具检查失败，请先安装 Node.js"
        return
    }
    Write-Host ""
    
    if (!(Reinstall-NpmPackages)) {
        Write-Log "WARN" "npm 包检查有问题，但继续执行..."
    }
    Write-Host ""
    
    if (!(Fix-ConfigFile)) {
        Write-Log "WARN" "配置文件修复有问题，但继续执行..."
    }
    Write-Host ""
    
    Configure-Firewall
    Write-Host ""
    
    if (!(Start-Koishi)) {
        Write-Log "ERROR" "Koishi 启动失败"
        Write-Log "INFO" "您可以手动运行: cd $ProjectPath; npm start"
        return
    }
    Write-Host ""
    
    if (Test-Access) {
        Show-AccessInfo
    } else {
        Write-Log "ERROR" "访问验证失败，但服务可能已启动"
        Show-AccessInfo
    }
    
    Write-Log "SUCCESS" "修复完成！"
}

# 执行主函数
Main
