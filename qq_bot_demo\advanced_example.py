# -*- coding: utf-8 -*-
"""
QQ机器人高级功能示例
包含更多实用功能的演示
"""

import asyncio
import json
import random
import re
from datetime import datetime, timedelta
from typing import Dict, List

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class AdvancedQQBot(botpy.Client):
    """高级QQ机器人示例"""
    
    def __init__(self):
        intents = botpy.Intents(
            public_guild_messages=True,
            direct_message=True,
            guilds=True,
            guild_message_reactions=True,
        )
        super().__init__(intents=intents)
        
        # 用户数据存储（实际项目中应使用数据库）
        self.user_data: Dict[str, Dict] = {}
        
        # 定时任务列表
        self.scheduled_tasks: List[Dict] = []

    async def on_ready(self):
        """机器人启动完成"""
        print(f"高级机器人 {self.robot.name} 已启动！")
        # 启动定时任务
        asyncio.create_task(self.scheduled_task_runner())

    async def on_at_message_create(self, message: Message):
        """处理@消息"""
        content = self.extract_content(message.content)
        
        # 高级命令处理
        if content.startswith('/'):
            await self.handle_advanced_command(message, content)
        else:
            await self.handle_smart_reply(message, content)

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容"""
        # 使用正则表达式更准确地提取内容
        pattern = r'<@!\d+>\s*'
        content = re.sub(pattern, '', raw_content).strip()
        return content

    async def handle_advanced_command(self, message: Message, content: str):
        """处理高级命令"""
        parts = content.split()
        command = parts[0][1:].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        command_map = {
            'remind': self.cmd_remind,
            'weather': self.cmd_weather_advanced,
            'translate': self.cmd_translate,
            'random': self.cmd_random,
            'calc': self.cmd_calculate,
            'userinfo': self.cmd_user_info,
            'joke': self.cmd_joke,
            'quote': self.cmd_quote,
        }
        
        handler = command_map.get(command)
        if handler:
            await handler(message, args)
        else:
            await self.send_message(message, f"未知命令: /{command}")

    async def cmd_remind(self, message: Message, args: list):
        """提醒功能"""
        if len(args) < 2:
            await self.send_message(message, 
                "用法: /remind <时间(分钟)> <提醒内容>\n例如: /remind 30 开会")
            return
        
        try:
            minutes = int(args[0])
            remind_text = " ".join(args[1:])
            
            # 添加到定时任务
            remind_time = datetime.now() + timedelta(minutes=minutes)
            task = {
                'type': 'remind',
                'time': remind_time,
                'channel_id': message.channel_id,
                'user_id': message.author.id,
                'content': remind_text
            }
            self.scheduled_tasks.append(task)
            
            await self.send_message(message, 
                f"⏰ 已设置提醒：{minutes}分钟后提醒你「{remind_text}」")
                
        except ValueError:
            await self.send_message(message, "时间必须是数字（分钟）")

    async def cmd_weather_advanced(self, message: Message, args: list):
        """高级天气查询（模拟）"""
        if not args:
            await self.send_message(message, "请提供城市名称")
            return
        
        city = " ".join(args)
        
        # 模拟天气数据
        weather_data = {
            "temperature": random.randint(-10, 35),
            "condition": random.choice(["晴", "多云", "阴", "小雨", "大雨", "雪"]),
            "humidity": random.randint(30, 90),
            "wind": random.randint(1, 8)
        }
        
        weather_msg = f"""🌤️ {city} 天气信息：

🌡️ 温度: {weather_data['temperature']}°C
☁️ 天气: {weather_data['condition']}
💧 湿度: {weather_data['humidity']}%
💨 风力: {weather_data['wind']}级

*这是模拟数据，实际使用请接入真实天气API*"""
        
        await self.send_message(message, weather_msg)

    async def cmd_translate(self, message: Message, args: list):
        """翻译功能（模拟）"""
        if not args:
            await self.send_message(message, "用法: /translate <要翻译的文本>")
            return
        
        text = " ".join(args)
        
        # 简单的中英文检测和模拟翻译
        if any('\u4e00' <= char <= '\u9fff' for char in text):
            # 包含中文，模拟翻译为英文
            result = f"[模拟翻译] {text} → Hello, this is a simulated translation."
        else:
            # 英文，模拟翻译为中文
            result = f"[模拟翻译] {text} → 你好，这是一个模拟翻译。"
        
        await self.send_message(message, f"🌐 翻译结果：\n{result}")

    async def cmd_random(self, message: Message, args: list):
        """随机数生成"""
        if len(args) == 0:
            # 生成1-100的随机数
            num = random.randint(1, 100)
            await self.send_message(message, f"🎲 随机数: {num}")
        elif len(args) == 1:
            try:
                max_num = int(args[0])
                num = random.randint(1, max_num)
                await self.send_message(message, f"🎲 随机数 (1-{max_num}): {num}")
            except ValueError:
                await self.send_message(message, "请输入有效的数字")
        elif len(args) == 2:
            try:
                min_num = int(args[0])
                max_num = int(args[1])
                num = random.randint(min_num, max_num)
                await self.send_message(message, f"🎲 随机数 ({min_num}-{max_num}): {num}")
            except ValueError:
                await self.send_message(message, "请输入有效的数字")

    async def cmd_calculate(self, message: Message, args: list):
        """简单计算器"""
        if not args:
            await self.send_message(message, "用法: /calc <表达式>\n例如: /calc 2+3*4")
            return
        
        expression = "".join(args)
        
        try:
            # 安全的数学表达式计算
            allowed_chars = set('0123456789+-*/.() ')
            if not all(c in allowed_chars for c in expression):
                await self.send_message(message, "表达式包含不允许的字符")
                return
            
            result = eval(expression)
            await self.send_message(message, f"🧮 计算结果: {expression} = {result}")
            
        except Exception as e:
            await self.send_message(message, f"计算错误: {str(e)}")

    async def cmd_user_info(self, message: Message, args: list):
        """用户信息"""
        user_id = message.author.id
        username = message.author.username
        
        # 获取或创建用户数据
        if user_id not in self.user_data:
            self.user_data[user_id] = {
                'first_seen': datetime.now(),
                'message_count': 0,
                'last_active': datetime.now()
            }
        
        user_info = self.user_data[user_id]
        user_info['message_count'] += 1
        user_info['last_active'] = datetime.now()
        
        info_msg = f"""👤 用户信息：

🏷️ 用户名: {username}
🆔 用户ID: {user_id}
📅 首次见面: {user_info['first_seen'].strftime('%Y-%m-%d %H:%M')}
💬 消息数量: {user_info['message_count']}
⏰ 最后活跃: {user_info['last_active'].strftime('%Y-%m-%d %H:%M')}"""
        
        await self.send_message(message, info_msg)

    async def cmd_joke(self, message: Message, args: list):
        """随机笑话"""
        jokes = [
            "为什么程序员喜欢黑暗？因为光明会产生bug！",
            "有10种人：懂二进制的和不懂二进制的。",
            "程序员的三大美德：懒惰、急躁和傲慢。",
            "为什么程序员总是搞混圣诞节和万圣节？因为Oct 31 == Dec 25！",
            "世界上最遥远的距离，是我在if里，你在else里。",
        ]
        
        joke = random.choice(jokes)
        await self.send_message(message, f"😄 随机笑话：\n{joke}")

    async def cmd_quote(self, message: Message, args: list):
        """励志名言"""
        quotes = [
            "代码如诗，程序如画。",
            "优秀的程序员写代码，伟大的程序员重写代码。",
            "先让代码工作，然后让它变得优雅。",
            "调试代码比写代码难一倍，所以如果你写代码时已经用尽了智慧，那你就没有足够的智慧来调试它。",
            "任何傻瓜都能写出计算机能理解的代码，只有优秀的程序员才能写出人类能理解的代码。",
        ]
        
        quote = random.choice(quotes)
        await self.send_message(message, f"💡 编程名言：\n{quote}")

    async def handle_smart_reply(self, message: Message, content: str):
        """智能回复"""
        content_lower = content.lower()
        
        # 更智能的关键词匹配
        if any(word in content_lower for word in ["你好", "hello", "hi", "嗨"]):
            replies = ["你好！很高兴见到你！😊", "嗨！有什么可以帮助你的吗？", "你好呀！今天过得怎么样？"]
            await self.send_message(message, random.choice(replies))
        
        elif any(word in content_lower for word in ["再见", "bye", "拜拜", "88"]):
            replies = ["再见！期待下次聊天！👋", "拜拜！记得想我哦～", "再见！愿你有美好的一天！"]
            await self.send_message(message, random.choice(replies))
        
        elif any(word in content_lower for word in ["谢谢", "thank", "感谢"]):
            replies = ["不客气！很高兴能帮到你！😄", "不用谢～这是我应该做的！", "能帮到你我也很开心！"]
            await self.send_message(message, random.choice(replies))
        
        elif "机器人" in content_lower or "bot" in content_lower:
            await self.send_message(message, "是的，我是一个QQ机器人！我可以聊天、执行命令、设置提醒等等。输入 /help 查看我的功能！")
        
        else:
            # 默认回复
            replies = [
                "我听到了你说的话！有什么需要帮助的吗？",
                "有趣的想法！你可以试试我的各种命令功能～",
                "我在认真听你说话呢！输入 /help 看看我能做什么吧！"
            ]
            await self.send_message(message, random.choice(replies))

    async def scheduled_task_runner(self):
        """定时任务执行器"""
        while True:
            try:
                current_time = datetime.now()
                completed_tasks = []
                
                for i, task in enumerate(self.scheduled_tasks):
                    if current_time >= task['time']:
                        if task['type'] == 'remind':
                            await self.send_remind_message(task)
                        completed_tasks.append(i)
                
                # 移除已完成的任务
                for i in reversed(completed_tasks):
                    self.scheduled_tasks.pop(i)
                
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                print(f"定时任务执行错误: {e}")
                await asyncio.sleep(60)

    async def send_remind_message(self, task: Dict):
        """发送提醒消息"""
        try:
            remind_msg = f"⏰ 提醒时间到！\n📝 {task['content']}"
            await self.api.post_message(
                channel_id=task['channel_id'],
                content=remind_msg
            )
        except Exception as e:
            print(f"发送提醒消息失败: {e}")

    async def send_message(self, original_message: Message, content: str):
        """发送消息"""
        try:
            await self.api.post_message(
                channel_id=original_message.channel_id,
                content=content,
                msg_id=original_message.id
            )
        except Exception as e:
            print(f"发送消息失败: {e}")


def main():
    """运行高级机器人"""
    if not all([BOT_CONFIG["appid"], BOT_CONFIG["token"]]):
        print("❌ 请配置机器人信息")
        return
    
    bot = AdvancedQQBot()
    
    try:
        print("🚀 启动高级QQ机器人...")
        bot.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
    except KeyboardInterrupt:
        print("\n👋 机器人已停止")
    except Exception as e:
        print(f"❌ 运行错误: {e}")


if __name__ == "__main__":
    main()
