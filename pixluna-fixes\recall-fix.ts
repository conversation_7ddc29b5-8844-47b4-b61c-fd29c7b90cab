// 修复后的 recall.ts 文件
import type { Bot, Context, Session } from 'koishi'

// 添加撤回限制检查
const RECALL_TIME_LIMIT = 2 * 60 * 1000 // 2分钟
const messageTimestamps = new Map<string, number>()

export async function deleteMessage(
  ctx: Context,
  bot: Bot,
  channelId: string,
  messageId: string
) {
  try {
    // 检查消息是否在撤回时间限制内
    const messageTime = messageTimestamps.get(messageId)
    if (messageTime && Date.now() - messageTime > RECALL_TIME_LIMIT) {
      ctx.logger('recall').warn('消息超过撤回时间限制', {
        channelId,
        messageId,
        elapsed: Date.now() - messageTime
      })
      return false
    }

    // 检查是否为沙盒环境
    const isSandbox = bot.config?.sandbox || 
                     (bot as any).endpoint?.includes('sandbox')
    
    if (isSandbox) {
      ctx.logger('recall').warn('沙盒环境不支持消息撤回', {
        channelId,
        messageId
      })
      return false
    }

    // 尝试撤回消息
    await bot.deleteMessage(channelId, messageId)
    ctx.logger('recall').debug('消息撤回成功', { channelId, messageId })
    
    // 清理时间戳记录
    messageTimestamps.delete(messageId)
    return true
    
  } catch (error) {
    // 详细的错误处理
    const errorCode = error.response?.data?.code || error.code
    const errorMessage = error.response?.data?.message || error.message
    
    // 特定错误码处理
    switch (errorCode) {
      case 11255:
        ctx.logger('recall').warn('消息撤回失败: 无效请求 (可能是权限不足或消息已过期)', {
          channelId,
          messageId,
          errorCode,
          errorMessage
        })
        break
      case 11254:
        ctx.logger('recall').warn('消息撤回失败: 消息不存在', {
          channelId,
          messageId,
          errorCode
        })
        break
      default:
        ctx.logger('recall').warn('撤回消息时发生错误', {
          channelId,
          messageId,
          error: {
            code: errorCode,
            message: errorMessage,
            status: error.response?.status
          }
        })
    }
    
    // 清理时间戳记录
    messageTimestamps.delete(messageId)
    return false
  }
}

export async function setupAutoRecall(
  ctx: Context,
  session: Session,
  messageIds: string[],
  timeout: number = 60000
) {
  if (!messageIds?.length) return

  const logger = ctx.logger('recall')
  
  // 验证配置
  if (timeout < 1000) {
    logger.warn('撤回延迟时间过短，调整为最小值 1 秒')
    timeout = 1000
  }
  
  if (timeout > 120000) {
    logger.warn('撤回延迟时间过长，调整为最大值 2 分钟')
    timeout = 120000
  }

  // 记录消息时间戳
  const currentTime = Date.now()
  messageIds.forEach(id => {
    messageTimestamps.set(id, currentTime)
  })

  logger.debug('设置消息自动撤回', {
    channelId: session.channelId,
    messageIds,
    timeout,
    messageCount: messageIds.length
  })

  // 检查机器人权限
  try {
    const bot = session.bot
    if (!bot) {
      logger.error('无法获取机器人实例')
      return
    }

    // 延迟撤回
    setTimeout(async () => {
      logger.debug('开始执行自动撤回', { 
        messageIds, 
        channelId: session.channelId 
      })
      
      let successCount = 0
      let failCount = 0
      
      for (const messageId of messageIds) {
        try {
          const success = await deleteMessage(
            ctx, 
            bot, 
            session.channelId, 
            messageId
          )
          
          if (success) {
            successCount++
          } else {
            failCount++
          }
          
          // 避免频繁请求
          await new Promise(resolve => setTimeout(resolve, 200))
          
        } catch (error) {
          failCount++
          logger.error('撤回消息异常', { 
            messageId, 
            error: error.message 
          })
        }
      }
      
      logger.info('自动撤回完成', {
        total: messageIds.length,
        success: successCount,
        failed: failCount,
        channelId: session.channelId
      })
      
    }, timeout)
    
  } catch (error) {
    logger.error('设置自动撤回失败', { 
      error: error.message,
      channelId: session.channelId 
    })
  }
}

// 清理过期的时间戳记录
setInterval(() => {
  const now = Date.now()
  for (const [messageId, timestamp] of messageTimestamps.entries()) {
    if (now - timestamp > RECALL_TIME_LIMIT * 2) {
      messageTimestamps.delete(messageId)
    }
  }
}, 5 * 60 * 1000) // 每5分钟清理一次
