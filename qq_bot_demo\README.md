# QQ机器人开发示例

这是一个基于QQ开放平台官方Python SDK开发的机器人示例项目。

## 功能特性

- ✅ 支持@机器人消息处理
- ✅ 支持私信消息处理
- ✅ 命令系统（/help, /ping, /time等）
- ✅ 智能聊天回复
- ✅ 完善的错误处理
- ✅ 日志记录功能
- ✅ 模块化设计

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置机器人

1. 在QQ开放平台创建机器人：https://bot.q.qq.com/open
2. 获取AppID、Token、AppSecret
3. 修改 `config.py` 文件中的配置信息

### 3. 运行机器人

```bash
python bot.py
```

## 项目结构

```
qq_bot_demo/
├── bot.py              # 机器人主程序
├── config.py           # 配置文件
├── requirements.txt    # 依赖列表
├── README.md          # 项目说明
└── logs/              # 日志目录（自动创建）
    └── bot.log        # 机器人日志
```

## 配置说明

### config.py 主要配置项：

- `BOT_CONFIG`: 机器人基本信息（AppID、Token等）
- `LOG_CONFIG`: 日志配置
- `FEATURES`: 功能开关和消息配置
- `API_CONFIG`: API相关配置

## 支持的命令

- `/help` - 显示帮助信息
- `/ping` - 测试机器人响应
- `/time` - 获取当前时间
- `/weather [城市]` - 查询天气（示例功能）

## 开发指南

### 添加新命令

1. 在 `handle_command` 方法的 `command_handlers` 字典中添加命令映射
2. 实现对应的命令处理函数（如 `cmd_your_command`）

### 自定义聊天回复

修改 `generate_chat_reply` 方法中的回复逻辑。

### 错误处理

所有主要功能都包含了try-catch错误处理，错误信息会记录到日志中。

## 注意事项

1. 确保在QQ开放平台正确配置了机器人权限
2. 机器人需要被添加到频道或群聊中才能接收消息
3. 私信功能需要用户主动发起对话
4. 请遵守QQ开放平台的使用规范

## 相关链接

- [QQ开放平台](https://bot.q.qq.com/open)
- [QQ机器人文档](https://bot.q.qq.com/wiki/)
- [Python SDK文档](https://bot.q.qq.com/wiki/develop/pythonsdk/)
- [官方Python SDK](https://github.com/tencent-connect/botpy)
