# 在您的 Linux 服务器上运行以下命令来修复 Koishi 访问问题

# 1. 停止当前的 Koishi 进程
pkill -f "npm.*start" || pkill -f koishi

# 2. 安装编译工具（这是关键步骤）
sudo apt update
sudo apt install -y build-essential python3 make g++ python3-dev

# 3. 进入 Koishi 项目目录
cd /root/koishi-bot

# 4. 清理并重新安装依赖包
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# 5. 重新启动 Koishi
npm start

# 或者使用后台启动：
# nohup npm start > logs/koishi.log 2>&1 &

# 6. 检查服务状态
# 等待几分钟后访问：http://您的服务器IP:5140

# 如果还是无法访问，检查防火墙：
sudo ufw allow 5140
# 或者
sudo iptables -A INPUT -p tcp --dport 5140 -j ACCEPT

# 查看日志：
tail -f logs/koishi.log
