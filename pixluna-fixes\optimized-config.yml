# Koishi pixluna 插件优化配置
# 解决 "Not Found" 和消息撤回错误

plugins:
  # pixluna 插件配置
  pixluna:
    # 基础设置
    isR18: false
    r18P: 0.1
    excludeAI: true  # 排除AI作品，提高成功率
    
    # 网络设置
    isProxy: false  # 如果网络有问题可以启用
    proxyHost: "http://127.0.0.1:7890"
    baseUrl: "i.pixiv.re"  # 使用稳定的反代服务
    
    # 并发和性能设置
    maxConcurrency: 1  # 降低并发避免被限制
    
    # 消息设置
    forwardMessage: true
    messageBefore: "正在获取图片..."
    showTags: true
    
    # 图片处理设置
    imageProcessing:
      confusion: false  # 暂时关闭混淆
      compress: true    # 启用压缩提高成功率
      compressQuality: 0.7
      isFlip: false
      flipMode: "horizontal"
    
    # 自动撤回设置 - 重点修复
    autoRecall:
      enable: false  # 暂时禁用自动撤回
      delay: 30      # 如果启用，设置为30秒
    
    # 图片源设置 - 使用多个备用源
    defaultSourceProvider:
      - "lolicon"    # 主要源
      - "safebooru"  # 备用源1
      - "gelbooru"   # 备用源2
    
    # 日志设置
    isLog: true  # 启用详细日志便于调试
    
    # API 配置 (如果需要)
    # pixiv:
    #   phpSESSID: ""
    #   userId: ""
    
    # danbooru:
    #   keyPairs: []
    
    # e621:
    #   keyPairs: []
    
    # gelbooru:
    #   keyPairs: []

  # QQ 适配器优化配置
  adapter-qq:
    # 基础配置
    bots:
      - appid: "你的机器人APPID"
        secret: "你的机器人密钥"
        token: "你的机器人令牌"
    
    # 环境设置
    sandbox: false  # 使用正式环境，沙盒环境功能受限
    
    # 网络优化
    timeout: 15000   # 增加超时时间
    retries: 2       # 启用重试
    
    # 错误处理
    handleError: true
    
  # 其他相关插件配置
  recall:
    # 禁用独立的撤回插件避免冲突
    enabled: false
    
  # HTTP 插件配置
  http:
    timeout: 30000   # 增加HTTP超时时间
    retries: 3       # 启用重试机制

# 全局配置
delay:
  # 消息发送延迟
  message: 500
  
  # 撤回延迟 (如果启用)
  recall: 30000

# 日志配置
logger:
  levels:
    base: 2
    pixluna: 1      # 启用 pixluna 详细日志
    adapter-qq: 1   # 启用 QQ 适配器详细日志
    recall: 1       # 启用撤回功能详细日志
    http: 1         # 启用 HTTP 请求日志

# 数据库配置 (如果使用)
database:
  # 根据实际情况配置
  # host: localhost
  # port: 3306
  # user: root
  # password: ''
  # database: koishi
