#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查botpy版本和API
"""

def check_botpy():
    try:
        import botpy
        print(f"✅ botpy版本: {botpy.__version__}")
        
        # 检查Client类的方法
        client_methods = [method for method in dir(botpy.Client) if not method.startswith('_')]
        print(f"📋 Client可用方法: {len(client_methods)}个")
        
        # 检查run方法的签名
        import inspect
        run_signature = inspect.signature(botpy.Client.run)
        print(f"🔍 run方法参数: {run_signature}")
        
        # 检查start方法的签名
        start_signature = inspect.signature(botpy.Client.start)
        print(f"🔍 start方法参数: {start_signature}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查botpy失败: {e}")
        return False

def test_simple_import():
    """测试简单导入"""
    try:
        import botpy
        from botpy.types.message import Message
        
        print("✅ 基础导入成功")
        
        # 测试Intents
        intents = botpy.Intents(public_guild_messages=True)
        print("✅ Intents创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 检查botpy SDK...")
    print("=" * 50)
    
    if test_simple_import():
        check_botpy()
    
    print("=" * 50)
    print("完成检查")
