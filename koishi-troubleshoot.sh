#!/bin/bash

#==============================================================================
# Koishi 部署故障排除脚本
# 
# 用于诊断和修复 Koishi 部署过程中的常见问题
# 
# 使用方法：
#   ./koishi-troubleshoot.sh
#   ./koishi-troubleshoot.sh --fix-apt
#   ./koishi-troubleshoot.sh --check-all
#
# 版本：v1.0.0
#==============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    
    case "$level" in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "FIX")
            echo -e "${GREEN}[FIX]${NC} $message"
            ;;
    esac
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查网络连接
check_network() {
    log "INFO" "检查网络连接..."
    
    local test_urls=(
        "8.8.8.8"
        "google.com"
        "github.com"
        "nodejs.org"
        "registry.npmjs.org"
    )
    
    local failed_count=0
    
    for url in "${test_urls[@]}"; do
        if ping -c 1 -W 3 "$url" >/dev/null 2>&1; then
            log "SUCCESS" "可以访问 $url"
        else
            log "ERROR" "无法访问 $url"
            ((failed_count++))
        fi
    done
    
    if [ $failed_count -eq 0 ]; then
        log "SUCCESS" "网络连接正常"
        return 0
    elif [ $failed_count -lt 3 ]; then
        log "WARNING" "部分网络连接异常，可能影响下载速度"
        return 1
    else
        log "ERROR" "网络连接严重异常，请检查网络配置"
        return 2
    fi
}

# 检查系统资源
check_system_resources() {
    log "INFO" "检查系统资源..."
    
    # 检查内存
    local mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_available=$(grep MemAvailable /proc/meminfo | awk '{print $2}')
    local mem_gb=$((mem_total / 1024 / 1024))
    local mem_avail_gb=$((mem_available / 1024 / 1024))
    
    log "INFO" "总内存: ${mem_gb}GB, 可用内存: ${mem_avail_gb}GB"
    
    if [ $mem_gb -lt 1 ]; then
        log "ERROR" "内存不足 1GB，可能导致安装失败"
        return 1
    elif [ $mem_avail_gb -lt 1 ]; then
        log "WARNING" "可用内存不足 1GB，建议释放内存"
    else
        log "SUCCESS" "内存充足"
    fi
    
    # 检查磁盘空间
    local disk_free=$(df / | awk 'NR==2 {print $4}')
    local disk_gb=$((disk_free / 1024 / 1024))
    
    log "INFO" "可用磁盘空间: ${disk_gb}GB"
    
    if [ $disk_gb -lt 2 ]; then
        log "ERROR" "磁盘空间不足 2GB"
        return 1
    else
        log "SUCCESS" "磁盘空间充足"
    fi
    
    return 0
}

# 检查包管理器状态
check_package_manager() {
    log "INFO" "检查包管理器状态..."
    
    if command_exists apt; then
        log "INFO" "检测到 APT 包管理器"
        check_apt_status
    elif command_exists yum; then
        log "INFO" "检测到 YUM 包管理器"
        check_yum_status
    elif command_exists dnf; then
        log "INFO" "检测到 DNF 包管理器"
        check_dnf_status
    elif command_exists pacman; then
        log "INFO" "检测到 Pacman 包管理器"
        check_pacman_status
    else
        log "ERROR" "未检测到支持的包管理器"
        return 1
    fi
}

# 检查 APT 状态
check_apt_status() {
    log "INFO" "检查 APT 状态..."
    
    # 检查锁文件
    if [ -f /var/lib/dpkg/lock-frontend ]; then
        if lsof /var/lib/dpkg/lock-frontend >/dev/null 2>&1; then
            log "ERROR" "APT 被其他进程锁定"
            log "FIX" "等待其他 APT 进程完成，或运行: sudo killall apt apt-get"
            return 1
        fi
    fi
    
    # 检查损坏的包
    local broken_packages=$(dpkg -l | grep "^..r" | wc -l)
    if [ $broken_packages -gt 0 ]; then
        log "WARNING" "检测到 $broken_packages 个损坏的包"
        log "FIX" "运行修复命令: sudo apt --fix-broken install"
    fi
    
    # 检查软件源
    if ! apt-cache policy >/dev/null 2>&1; then
        log "ERROR" "APT 软件源配置异常"
        log "FIX" "检查 /etc/apt/sources.list 配置"
        return 1
    fi
    
    log "SUCCESS" "APT 状态正常"
    return 0
}

# 检查 YUM 状态
check_yum_status() {
    log "INFO" "检查 YUM 状态..."
    
    # 检查锁文件
    if [ -f /var/run/yum.pid ]; then
        log "WARNING" "YUM 可能被其他进程使用"
    fi
    
    # 检查仓库
    if ! yum repolist >/dev/null 2>&1; then
        log "ERROR" "YUM 仓库配置异常"
        return 1
    fi
    
    log "SUCCESS" "YUM 状态正常"
    return 0
}

# 检查 DNF 状态
check_dnf_status() {
    log "INFO" "检查 DNF 状态..."
    
    # 检查仓库
    if ! dnf repolist >/dev/null 2>&1; then
        log "ERROR" "DNF 仓库配置异常"
        return 1
    fi
    
    log "SUCCESS" "DNF 状态正常"
    return 0
}

# 检查 Pacman 状态
check_pacman_status() {
    log "INFO" "检查 Pacman 状态..."
    
    # 检查锁文件
    if [ -f /var/lib/pacman/db.lck ]; then
        log "ERROR" "Pacman 被锁定"
        log "FIX" "删除锁文件: sudo rm /var/lib/pacman/db.lck"
        return 1
    fi
    
    log "SUCCESS" "Pacman 状态正常"
    return 0
}

# 修复 APT 问题
fix_apt_issues() {
    log "INFO" "修复 APT 问题..."
    
    # 杀死可能的 APT 进程
    sudo killall apt apt-get dpkg >/dev/null 2>&1 || true
    
    # 删除锁文件
    sudo rm -f /var/lib/dpkg/lock-frontend
    sudo rm -f /var/lib/dpkg/lock
    sudo rm -f /var/cache/apt/archives/lock
    
    # 重新配置 dpkg
    sudo dpkg --configure -a
    
    # 修复损坏的包
    sudo apt --fix-broken install -y
    
    # 更新包缓存
    sudo apt update
    
    log "SUCCESS" "APT 修复完成"
}

# 检查 Node.js 环境
check_nodejs_env() {
    log "INFO" "检查 Node.js 环境..."
    
    if command_exists node; then
        local node_version=$(node --version)
        local major_version=$(echo "$node_version" | sed 's/v//' | cut -d. -f1)
        
        log "INFO" "Node.js 版本: $node_version"
        
        if [ "$major_version" -ge 18 ]; then
            log "SUCCESS" "Node.js 版本符合要求"
        else
            log "ERROR" "Node.js 版本过低，需要 v18+"
            return 1
        fi
    else
        log "WARNING" "未安装 Node.js"
        return 1
    fi
    
    if command_exists npm; then
        local npm_version=$(npm --version)
        log "INFO" "npm 版本: $npm_version"
        
        # 检查 npm 配置
        local npm_prefix=$(npm config get prefix)
        log "INFO" "npm 全局目录: $npm_prefix"
        
        # 检查权限
        if [ ! -w "$npm_prefix" ]; then
            log "WARNING" "npm 全局目录无写权限"
            log "FIX" "修复权限: sudo chown -R $(whoami) $npm_prefix"
        fi
    else
        log "ERROR" "未安装 npm"
        return 1
    fi
    
    return 0
}

# 清理系统
cleanup_system() {
    log "INFO" "清理系统..."
    
    # 清理包管理器缓存
    if command_exists apt; then
        sudo apt autoremove -y
        sudo apt autoclean
    elif command_exists yum; then
        sudo yum clean all
    elif command_exists dnf; then
        sudo dnf clean all
    elif command_exists pacman; then
        sudo pacman -Scc --noconfirm
    fi
    
    # 清理临时文件
    sudo rm -rf /tmp/koishi-*
    sudo rm -rf /tmp/node-*
    
    log "SUCCESS" "系统清理完成"
}

# 生成诊断报告
generate_report() {
    local report_file="/tmp/koishi-diagnostic-$(date +%Y%m%d_%H%M%S).txt"
    
    log "INFO" "生成诊断报告: $report_file"
    
    {
        echo "Koishi 部署诊断报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo
        
        echo "系统信息:"
        uname -a
        echo
        
        if [ -f /etc/os-release ]; then
            echo "发行版信息:"
            cat /etc/os-release
            echo
        fi
        
        echo "内存信息:"
        free -h
        echo
        
        echo "磁盘信息:"
        df -h
        echo
        
        echo "网络接口:"
        ip addr show
        echo
        
        if command_exists node; then
            echo "Node.js 版本: $(node --version)"
        else
            echo "Node.js: 未安装"
        fi
        
        if command_exists npm; then
            echo "npm 版本: $(npm --version)"
            echo "npm 配置:"
            npm config list
        else
            echo "npm: 未安装"
        fi
        echo
        
        echo "环境变量:"
        env | grep -E "(PATH|NODE|NPM)" | sort
        echo
        
        echo "最近的错误日志:"
        if [ -f /tmp/koishi-deploy-*.log ]; then
            tail -50 /tmp/koishi-deploy-*.log
        fi
        
    } > "$report_file"
    
    log "SUCCESS" "诊断报告已生成: $report_file"
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "========================================"
    echo "    Koishi 部署故障排除脚本 v1.0.0"
    echo "========================================"
    echo -e "${NC}"
    
    case "${1:-}" in
        --fix-apt)
            fix_apt_issues
            ;;
        --check-all)
            check_network
            check_system_resources
            check_package_manager
            check_nodejs_env
            ;;
        --cleanup)
            cleanup_system
            ;;
        --report)
            generate_report
            ;;
        --help|-h)
            echo "使用方法:"
            echo "  $0                # 交互式诊断"
            echo "  $0 --fix-apt      # 修复 APT 问题"
            echo "  $0 --check-all    # 检查所有组件"
            echo "  $0 --cleanup      # 清理系统"
            echo "  $0 --report       # 生成诊断报告"
            exit 0
            ;;
        "")
            # 交互式模式
            log "INFO" "开始系统诊断..."
            
            check_network
            echo
            check_system_resources
            echo
            check_package_manager
            echo
            check_nodejs_env
            echo
            
            read -p "是否生成详细诊断报告？(y/N): " -r
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                generate_report
            fi
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 $0 --help 查看帮助"
            exit 1
            ;;
    esac
    
    log "SUCCESS" "诊断完成"
}

# 执行主函数
main "$@"
