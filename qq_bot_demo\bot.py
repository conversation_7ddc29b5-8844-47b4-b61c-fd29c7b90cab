# -*- coding: utf-8 -*-
"""
QQ机器人主程序
基于官方botpy SDK开发
"""

import asyncio
import logging
import os
import traceback
from datetime import datetime
from typing import Optional

import botpy
from botpy import logging as botpy_logging
from botpy.types.message import Message

from config import BOT_CONFIG, LOG_CONFIG, FEATURES, API_CONFIG


class QQBot(botpy.Client):
    """QQ机器人主类"""
    
    def __init__(self):
        # 设置事件订阅 - 添加所有消息事件
        intents = botpy.Intents(
            public_guild_messages=True,  # 公域消息事件
            direct_message=True,  # 私信事件
            guilds=True,  # 频道事件
            guild_message_reactions=True,  # 消息互动事件
            guild_messages=True,  # 频道消息事件（包括非@消息）
        )
        
        super().__init__(intents=intents)
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        # 创建logs目录
        if LOG_CONFIG["file_enabled"]:
            os.makedirs(os.path.dirname(LOG_CONFIG["file_path"]), exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=getattr(logging, LOG_CONFIG["level"]),
            format=LOG_CONFIG["format"],
            handlers=[
                logging.StreamHandler(),  # 控制台输出
                logging.FileHandler(LOG_CONFIG["file_path"], encoding='utf-8') 
                if LOG_CONFIG["file_enabled"] else logging.NullHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("QQ机器人初始化完成")

    async def on_ready(self):
        """机器人启动完成事件"""
        self.logger.info(f"机器人 {self.robot.name} 已上线！")
        self.logger.info(f"机器人ID: {self.robot.id}")

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息"""
        try:
            self.logger.info(f"收到@消息: {message.content} (来自: {message.author.username})")
            
            # 获取消息内容（去除@机器人部分）
            content = self.extract_content(message.content)
            
            # 处理命令
            if content.startswith(FEATURES["command_prefix"]):
                await self.handle_command(message, content)
            else:
                # 普通聊天回复
                await self.handle_chat(message, content)
                
        except Exception as e:
            self.logger.error(f"处理@消息时发生错误: {e}")
            self.logger.error(traceback.format_exc())
            await self.send_error_message(message)

    async def on_direct_message_create(self, message: Message):
        """处理私信消息"""
        try:
            self.logger.info(f"收到私信: {message.content} (来自: {message.author.username})")

            content = message.content.strip()

            # 处理命令
            if content.startswith(FEATURES["command_prefix"]):
                await self.handle_command(message, content)
            else:
                # 普通聊天回复
                await self.handle_chat(message, content)

        except Exception as e:
            self.logger.error(f"处理私信时发生错误: {e}")
            self.logger.error(traceback.format_exc())
            await self.send_error_message(message)

    async def on_message_create(self, message: Message):
        """处理所有消息（包括非@消息）- 自动识别关键词回复"""
        try:
            # 检查是否是机器人自己发的消息
            if message.author.bot:
                return

            # 检查是否已经是@消息（避免重复处理）
            if f"<@!{self.robot.id}>" in message.content:
                return  # @消息由on_at_message_create处理

            content = message.content.strip()
            if not content:
                return

            self.logger.info(f"收到普通消息: {content} (来自: {message.author.username})")
            print(f"💬 收到普通消息: {content}")

            # 检查是否包含关键词，如果包含就回复
            content_lower = content.lower()

            # 检测关键词
            should_reply = False
            reply_type = ""

            if any(word in content_lower for word in ["你好", "hello", "hi", "嗨", "您好"]):
                should_reply = True
                reply_type = "问候"
                print(f"🎯 检测到问候关键词")
            elif any(word in content_lower for word in ["再见", "bye", "拜拜", "goodbye"]):
                should_reply = True
                reply_type = "告别"
                print(f"🎯 检测到告别关键词")
            elif any(word in content_lower for word in ["谢谢", "thank", "感谢", "thanks"]):
                should_reply = True
                reply_type = "感谢"
                print(f"🎯 检测到感谢关键词")
            elif any(word in content_lower for word in ["机器人", "bot", "助手"]):
                should_reply = True
                reply_type = "机器人"
                print(f"🎯 检测到机器人关键词")
            elif any(word in content_lower for word in ["时间", "几点", "现在"]):
                should_reply = True
                reply_type = "时间"
                print(f"🎯 检测到时间关键词")

            # 如果检测到关键词，就回复
            if should_reply:
                print(f"✅ 准备自动回复 ({reply_type})")
                await self.handle_chat(message, content)
            else:
                print(f"ℹ️ 未检测到关键词，不回复")

        except Exception as e:
            self.logger.error(f"处理普通消息时发生错误: {e}")
            self.logger.error(traceback.format_exc())

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容，去除@机器人部分"""
        # 简单的内容提取，实际可能需要更复杂的处理
        content = raw_content.strip()
        # 去除可能的@机器人标记
        if content.startswith('<@!'):
            # 找到>的位置，去除@部分
            end_pos = content.find('>')
            if end_pos != -1:
                content = content[end_pos + 1:].strip()
        return content

    async def handle_command(self, message: Message, content: str):
        """处理命令"""
        command_parts = content.split()
        command = command_parts[0][1:]  # 去除前缀
        args = command_parts[1:] if len(command_parts) > 1 else []
        
        self.logger.info(f"执行命令: {command}, 参数: {args}")
        
        # 命令路由
        command_handlers = {
            "help": self.cmd_help,
            "ping": self.cmd_ping,
            "time": self.cmd_time,
            "weather": self.cmd_weather,
        }
        
        handler = command_handlers.get(command)
        if handler:
            await handler(message, args)
        else:
            await self.send_message(message, f"未知命令: {command}\n输入 /help 查看可用命令")

    async def handle_chat(self, message: Message, content: str):
        """处理普通聊天"""
        if not content:
            return
            
        # 简单的聊天回复逻辑
        reply = self.generate_chat_reply(content)
        await self.send_message(message, reply)

    def generate_chat_reply(self, content: str) -> str:
        """生成聊天回复"""
        import random

        content_lower = content.lower()

        # 随机回复功能
        if any(word in content_lower for word in ["你好", "hello", "hi"]):
            hello_replies = [
                "你好！很高兴见到你！😊",
                "嗨！今天过得怎么样？🌟",
                "你好呀！有什么我可以帮助你的吗？😄",
                "Hello！欢迎来聊天！👋",
                "你好！我是QQ机器人，很开心认识你！🤖",
                "嗨嗨！今天天气不错呢～☀️",
                "你好！感谢你和我打招呼！💕",
                "Hello World！程序员的经典问候！💻",
                "你好！要不要聊聊天？😊",
                "嗨！我正在等你呢！✨",
                "你好！今天是美好的一天！🌈",
                "Hello！很高兴在这里遇见你！🎉",
                "你好！有什么有趣的事情想分享吗？😄",
                "嗨！我刚刚在想你呢！💭",
                "你好！准备好开始愉快的聊天了吗？🎈"
            ]
            return random.choice(hello_replies)
        elif any(word in content_lower for word in ["再见", "bye", "拜拜"]):
            bye_replies = [
                "再见！期待下次聊天！👋",
                "拜拜！记得想我哦～😘",
                "再见！愿你每天都开心！🌟",
                "Bye bye！下次见！✨",
                "再见！保重身体！💪",
                "拜拜！期待我们的下次相遇！🤗",
                "再见！今天聊得很开心！😊",
                "拜拜！记得常来找我玩！🎮"
            ]
            return random.choice(bye_replies)
        elif any(word in content_lower for word in ["谢谢", "thank"]):
            thank_replies = [
                "不客气！很高兴能帮到你！😄",
                "不用谢！这是我应该做的！😊",
                "客气什么！我们是朋友嘛！🤝",
                "不客气！有需要随时找我！💪",
                "谢谢你的感谢！😄",
                "能帮到你我很开心！🌟",
                "不用客气！互相帮助嘛！🤗"
            ]
            return random.choice(thank_replies)
        elif "天气" in content_lower:
            return "你可以使用 /weather [城市名] 命令查询天气哦！"
        elif "时间" in content_lower:
            return "你可以使用 /time 命令查询当前时间哦！"
        else:
            default_replies = [
                "我收到了你的消息！你可以输入 /help 查看我能做什么～",
                "有趣！告诉我更多吧！😊",
                "我在认真听呢！继续说～👂",
                "这个话题很有意思！💭",
                "我正在学习中，谢谢你的分享！📚",
                "哇，真的吗？听起来很棒！✨"
            ]
            return random.choice(default_replies)

    # 命令处理函数
    async def cmd_help(self, message: Message, args: list):
        """帮助命令"""
        await self.send_message(message, FEATURES["help_message"])

    async def cmd_ping(self, message: Message, args: list):
        """Ping命令"""
        await self.send_message(message, "🏓 Pong! 机器人运行正常！")

    async def cmd_time(self, message: Message, args: list):
        """时间命令"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        await self.send_message(message, f"🕐 当前时间: {current_time}")

    async def cmd_weather(self, message: Message, args: list):
        """天气命令（示例）"""
        if not args:
            await self.send_message(message, "请提供城市名称，例如: /weather 北京")
            return
            
        city = " ".join(args)
        # 这里是示例，实际需要接入天气API
        await self.send_message(message, f"🌤️ {city}的天气查询功能正在开发中...")

    async def send_message(self, original_message: Message, content: str):
        """发送消息的统一方法"""
        try:
            if hasattr(original_message, 'guild_id'):
                # 频道消息
                await self.api.post_message(
                    channel_id=original_message.channel_id,
                    content=content,
                    msg_id=original_message.id
                )
            else:
                # 私信消息
                await self.api.post_dms(
                    guild_id=original_message.guild_id,
                    content=content,
                    msg_id=original_message.id
                )
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")

    async def send_error_message(self, message: Message):
        """发送错误消息"""
        await self.send_message(message, "❌ 处理消息时发生错误，请稍后重试")


def main():
    """主函数"""
    # 检查配置
    if not all([BOT_CONFIG["appid"], BOT_CONFIG["token"]]):
        print("❌ 请在config.py中配置机器人的AppID和Token")
        return
    
    # 创建并运行机器人
    bot = QQBot()
    
    try:
        print("🚀 正在启动QQ机器人...")
        bot.run(
            appid=BOT_CONFIG["appid"],
            secret=BOT_CONFIG["secret"]
        )
    except KeyboardInterrupt:
        print("\n👋 机器人已停止运行")
    except Exception as e:
        print(f"❌ 机器人运行出错: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
