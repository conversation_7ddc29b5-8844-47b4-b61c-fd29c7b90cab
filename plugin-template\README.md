# koishi-plugin-example

一个功能丰富的 Koishi 插件示例，展示了插件开发的最佳实践。

## 🎯 功能特性

- ✅ **基础命令处理** - 支持命令、子命令、参数和选项
- ✅ **配置管理** - 完整的配置模式定义和验证
- ✅ **数据库操作** - 用户数据存储和查询
- ✅ **积分系统** - 每日签到、积分管理
- ✅ **事件监听** - 群组事件和消息监听
- ✅ **中间件** - 消息预处理和日志记录
- ✅ **权限管理** - 管理员权限检查
- ✅ **国际化** - 中英文语言包支持
- ✅ **错误处理** - 完善的异常处理机制
- ✅ **日志记录** - 详细的操作日志

## 📦 安装

```bash
npm install koishi-plugin-example
```

或者在 Koishi 控制台的插件市场中搜索 "example" 进行安装。

## ⚙️ 配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `prefix` | string | "Bot" | 回复消息的前缀 |
| `maxMessageLength` | number | 500 | 最大消息长度限制 |
| `enableLogging` | boolean | true | 是否启用详细日志记录 |
| `adminUsers` | string[] | [] | 管理员用户ID列表 |
| `pointsConfig.dailyBonus` | number | 10 | 每日签到奖励积分 |
| `pointsConfig.maxPoints` | number | 10000 | 积分上限 |
| `pointsConfig.enableReset` | boolean | false | 是否允许重置积分 |

## 🎮 使用方法

### 基础命令

```
example.hello [姓名]     # 打招呼
example.random          # 生成随机数
example.random -m 1 -M 10  # 生成1-10的随机数
```

### 积分系统

```
example.points.check    # 查看积分
example.points.signin   # 每日签到
积分                    # 快速查看积分
```

### 管理员命令

```
example.admin.reset <用户ID>  # 重置用户积分
example.admin.logs [数量]     # 查看操作日志
```

## 🗄️ 数据库表

插件会自动创建以下数据库表：

### example_users
- `id` - 主键
- `userId` - 用户ID
- `username` - 用户名
- `points` - 积分
- `lastSignIn` - 最后签到时间
- `createdAt` - 创建时间
- `updatedAt` - 更新时间

### example_logs
- `id` - 主键
- `userId` - 用户ID
- `action` - 操作类型
- `details` - 操作详情
- `timestamp` - 时间戳

## 🌍 国际化

插件支持中英文双语：

- 中文 (zh)
- English (en)

语言包位于 `src/locales/` 目录下。

## 🔧 开发

### 环境要求

- Node.js 18+
- Koishi 4.15.0+

### 本地开发

1. 克隆项目
```bash
git clone https://github.com/yourusername/koishi-plugin-example.git
cd koishi-plugin-example
```

2. 安装依赖
```bash
npm install
```

3. 构建项目
```bash
npm run build
```

4. 在 Koishi 项目中测试
```bash
# 在你的 Koishi 项目中
npm link /path/to/koishi-plugin-example
```

### 项目结构

```
koishi-plugin-example/
├── src/
│   ├── index.ts           # 主插件文件
│   └── locales/           # 国际化文件
│       ├── zh.yml         # 中文语言包
│       └── en.yml         # 英文语言包
├── lib/                   # 编译输出目录
├── package.json           # 包配置
├── tsconfig.json          # TypeScript 配置
└── README.md              # 说明文档
```

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 实现基础命令系统
- 添加积分系统
- 支持国际化
- 完善错误处理

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [Koishi 官方文档](https://koishi.chat/)
- [插件开发指南](https://koishi.chat/guide/)
- [插件市场](https://koishi.chat/market/)
- [GitHub 仓库](https://github.com/yourusername/koishi-plugin-example)

## 💡 灵感来源

这个插件模板参考了 Koishi 官方插件的最佳实践，旨在为开发者提供一个完整的插件开发示例。

---

如果这个插件对你有帮助，请给个 ⭐ Star！
