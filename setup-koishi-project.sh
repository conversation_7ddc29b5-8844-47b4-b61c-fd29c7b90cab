#!/bin/bash

# Koishi 项目完整设置脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

show_header() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi 项目完整设置工具"
    echo "========================================"
    echo -e "${NC}"
}

# 检查和安装依赖
setup_dependencies() {
    log "INFO" "检查和安装依赖..."
    
    # 检查 Node.js
    if ! command -v node >/dev/null 2>&1; then
        log "ERROR" "Node.js 未安装"
        exit 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    log "SUCCESS" "Node.js 版本: $(node --version)"
    
    # 检查 npm
    if ! command -v npm >/dev/null 2>&1; then
        log "ERROR" "npm 未安装"
        exit 1
    fi
    
    log "SUCCESS" "npm 版本: $(npm --version)"
    
    # 卸载错误的 cmdtest
    if command -v yarn >/dev/null 2>&1; then
        local yarn_path=$(which yarn)
        if [[ "$yarn_path" == *"cmdtest"* ]]; then
            log "WARN" "检测到错误的 yarn (cmdtest)，正在卸载..."
            apt remove -y cmdtest >/dev/null 2>&1
        fi
    fi
    
    # 安装正确的 Yarn
    if ! command -v yarn >/dev/null 2>&1 || [[ "$(which yarn)" == *"cmdtest"* ]]; then
        log "FIX" "安装 Yarn..."
        npm install -g yarn >/dev/null 2>&1
        
        if [ $? -eq 0 ]; then
            log "SUCCESS" "Yarn 安装成功: $(yarn --version)"
        else
            log "WARN" "Yarn 安装失败，将使用 npm"
        fi
    else
        log "SUCCESS" "Yarn 版本: $(yarn --version)"
    fi
}

# 配置镜像源
setup_registry() {
    log "INFO" "配置包管理器镜像源..."
    
    # 配置 npm 镜像
    npm config set registry https://registry.npmmirror.com
    log "SUCCESS" "npm 镜像已设置为: $(npm config get registry)"
    
    # 配置 yarn 镜像（如果可用）
    if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
        yarn config set registry https://registry.npmmirror.com >/dev/null 2>&1
        log "SUCCESS" "yarn 镜像已设置"
    fi
}

# 创建 Koishi 项目
create_koishi_project() {
    log "INFO" "创建 Koishi 项目..."
    
    # 获取项目名称
    read -p "请输入项目名称 (默认: my-koishi-bot): " PROJECT_NAME
    if [ -z "$PROJECT_NAME" ]; then
        PROJECT_NAME="my-koishi-bot"
    fi
    
    # 检查目录是否存在
    if [ -d "$PROJECT_NAME" ]; then
        log "WARN" "目录 $PROJECT_NAME 已存在"
        read -p "是否删除并重新创建? (y/N): " RECREATE
        if [[ $RECREATE == [yY] ]]; then
            rm -rf "$PROJECT_NAME"
            log "INFO" "已删除现有目录"
        else
            log "ERROR" "项目创建取消"
            exit 1
        fi
    fi
    
    # 尝试使用不同方法创建项目
    log "FIX" "正在创建 Koishi 项目..."
    
    # 方法1: 使用 yarn create (如果可用)
    if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
        log "INFO" "使用 yarn 创建项目..."
        yarn create koishi "$PROJECT_NAME" --template basic
        
        if [ $? -eq 0 ] && [ -d "$PROJECT_NAME" ]; then
            log "SUCCESS" "项目创建成功 (yarn)"
            return 0
        fi
    fi
    
    # 方法2: 使用 npm create
    log "INFO" "使用 npm 创建项目..."
    npm create koishi@latest "$PROJECT_NAME" -- --template basic
    
    if [ $? -eq 0 ] && [ -d "$PROJECT_NAME" ]; then
        log "SUCCESS" "项目创建成功 (npm)"
        return 0
    fi
    
    # 方法3: 手动创建项目
    log "WARN" "自动创建失败，使用手动方式..."
    manual_create_project "$PROJECT_NAME"
}

# 手动创建项目
manual_create_project() {
    local project_name=$1
    
    log "FIX" "手动创建 Koishi 项目结构..."
    
    # 创建项目目录
    mkdir -p "$project_name"
    cd "$project_name"
    
    # 创建 package.json
    cat > package.json << EOF
{
  "name": "$project_name",
  "version": "1.0.0",
  "description": "A Koishi Bot Project",
  "main": "lib/index.js",
  "scripts": {
    "build": "koishi build",
    "start": "koishi start",
    "dev": "koishi dev"
  },
  "keywords": ["koishi", "chatbot"],
  "license": "MIT",
  "dependencies": {
    "koishi": "^4.15.0",
    "@koishijs/plugin-console": "^5.0.0",
    "@koishijs/plugin-dataview": "^2.0.0",
    "@koishijs/plugin-help": "^1.0.0",
    "@koishijs/plugin-echo": "^1.0.0"
  },
  "devDependencies": {
    "@types/node": "^18.0.0",
    "typescript": "^4.9.0"
  }
}
EOF
    
    # 创建 koishi.yml
    cat > koishi.yml << EOF
host: 0.0.0.0
port: 5140

plugins:
  console:
    open: true
  
  dataview: {}
  
  help: {}
  
  echo: {}

logger:
  levels:
    base: 2
EOF
    
    # 创建 tsconfig.json
    cat > tsconfig.json << EOF
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "CommonJS",
    "lib": ["ES2020"],
    "declaration": true,
    "outDir": "lib",
    "rootDir": "src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node"
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "lib"
  ]
}
EOF
    
    # 创建源码目录
    mkdir -p src
    
    # 创建入口文件
    cat > src/index.ts << EOF
import { Context } from 'koishi'

export const name = 'my-plugin'

export function apply(ctx: Context) {
  ctx.command('hello', '打招呼')
    .action(({ session }) => {
      return \`Hello, \${session?.username}!\`
    })
}
EOF
    
    log "SUCCESS" "项目结构创建完成"
    cd ..
}

# 安装依赖
install_dependencies() {
    local project_name=$1
    
    log "INFO" "安装项目依赖..."
    cd "$project_name"
    
    # 选择包管理器
    if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
        log "INFO" "使用 yarn 安装依赖..."
        yarn install
    else
        log "INFO" "使用 npm 安装依赖..."
        npm install
    fi
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "依赖安装完成"
    else
        log "ERROR" "依赖安装失败"
        return 1
    fi
    
    cd ..
}

# 安装 pixluna 插件
install_pixluna() {
    local project_name=$1
    
    log "INFO" "安装 pixluna 插件..."
    cd "$project_name"
    
    # 安装插件
    if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
        yarn add koishi-plugin-pixluna
    else
        npm install koishi-plugin-pixluna
    fi
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "pixluna 插件安装完成"
        
        # 添加到配置文件
        log "FIX" "添加 pixluna 配置..."
        cat >> koishi.yml << EOF

  # pixluna 插件配置
  pixluna:
    # 基础设置
    isR18: false
    r18P: 0.1
    excludeAI: true
    
    # 禁用自动撤回避免错误
    autoRecall:
      enable: false
    
    # 启用压缩提高成功率
    imageProcessing:
      compress: true
      compressQuality: 0.7
    
    # 启用日志
    isLog: true
    
    # 图片源设置
    defaultSourceProvider:
      - "lolicon"
      - "safebooru"
EOF
        
        log "SUCCESS" "pixluna 配置已添加"
    else
        log "WARN" "pixluna 插件安装失败，可稍后手动安装"
    fi
    
    cd ..
}

# 构建和启动项目
build_and_start() {
    local project_name=$1
    
    log "INFO" "构建项目..."
    cd "$project_name"
    
    # 构建项目
    if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
        yarn build
    else
        npm run build
    fi
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "项目构建完成"
        
        # 询问是否启动
        echo
        read -p "是否立即启动 Koishi? (y/N): " START_NOW
        
        if [[ $START_NOW == [yY] ]]; then
            log "INFO" "启动 Koishi..."
            
            if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
                yarn start
            else
                npm start
            fi
        else
            log "INFO" "项目已准备就绪，使用以下命令启动:"
            echo "cd $project_name"
            if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
                echo "yarn start"
            else
                echo "npm start"
            fi
        fi
    else
        log "ERROR" "项目构建失败"
    fi
    
    cd ..
}

# 显示完成信息
show_completion() {
    local project_name=$1
    
    echo
    log "SUCCESS" "Koishi 项目设置完成！"
    echo
    echo -e "${BLUE}项目信息：${NC}"
    echo "项目名称: $project_name"
    echo "项目路径: $(pwd)/$project_name"
    echo
    echo -e "${BLUE}启动命令：${NC}"
    echo "cd $project_name"
    if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
        echo "yarn start"
    else
        echo "npm start"
    fi
    echo
    echo -e "${BLUE}访问地址：${NC}"
    echo "http://localhost:5140"
    echo
    echo -e "${BLUE}常用命令：${NC}"
    if command -v yarn >/dev/null 2>&1 && [[ "$(which yarn)" != *"cmdtest"* ]]; then
        echo "yarn build    # 构建项目"
        echo "yarn dev      # 开发模式"
        echo "yarn start    # 启动项目"
    else
        echo "npm run build # 构建项目"
        echo "npm run dev   # 开发模式"
        echo "npm start     # 启动项目"
    fi
    echo
    echo -e "${GREEN}设置完成！祝您使用愉快！${NC}"
}

# 主函数
main() {
    show_header
    
    log "INFO" "开始设置 Koishi 项目..."
    echo
    
    setup_dependencies
    setup_registry
    
    create_koishi_project
    
    # 获取项目名称
    if [ -z "$PROJECT_NAME" ]; then
        PROJECT_NAME="my-koishi-bot"
    fi
    
    install_dependencies "$PROJECT_NAME"
    install_pixluna "$PROJECT_NAME"
    build_and_start "$PROJECT_NAME"
    show_completion "$PROJECT_NAME"
}

main "$@"
