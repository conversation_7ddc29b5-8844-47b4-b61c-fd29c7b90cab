# Koishi Docker 容器管理功能

## 🎯 功能概述

新增的容器管理功能为 Koishi Docker 部署脚本提供了智能的容器检测和交互式管理能力，避免了容器冲突和重复部署的问题。

## 🔍 检测功能

### 自动检测内容
- **现有容器**: 检测所有 Koishi 相关容器（通过名称和端口）
- **端口占用**: 检测指定端口是否被占用
- **目标容器**: 检测是否存在同名的目标容器
- **运行状态**: 区分容器是否正在运行

### 检测时机
- 在端口选择完成后
- 在实际部署开始前
- 确保 Docker 服务可用后

## 🎮 交互式菜单

当检测到现有容器或端口冲突时，会显示交互式菜单：

```
请选择操作：
1) 查看现有容器状态和日志
2) 重启现有容器 (如果存在目标容器)
3) 停止并重新部署新容器
4) 取消操作并退出脚本
```

### 选项详解

#### 1️⃣ 查看容器状态
- 显示所有 Koishi 相关容器
- 显示端口占用情况
- 显示目标容器详细信息
- 显示容器日志（最后10行）
- 查看后返回菜单继续选择

#### 2️⃣ 重启现有容器
- 仅在目标容器存在时可用
- 带进度显示的重启过程：
  ```
  [进度] 停止容器 [████████████████████] 100% (1/4)
  [进度] 启动容器 [████████████████████] 100% (2/4)
  [进度] 等待服务就绪 [████████████████████] 100% (3/4)
  [进度] 验证服务访问 [████████████████████] 100% (4/4)
  ```
- 自动验证服务可用性
- 显示访问信息后退出

#### 3️⃣ 重新部署
- 停止并删除所有相关容器
- 释放被占用的端口
- 继续正常的部署流程
- 确保环境清洁

#### 4️⃣ 取消操作
- 安全退出脚本
- 不对现有环境做任何修改

## 📊 进度显示

### 重启容器进度
```
[进度] 停止容器 [████████████████████] 100% (1/4)
✓ 容器已停止

[进度] 启动容器 [████████████████████] 100% (2/4)
✓ 容器已启动

[进度] 等待服务就绪 [████████████████████] 100% (3/4)
✓ 服务就绪

[进度] 验证服务访问 [████████████████████] 100% (4/4)
✓ 服务访问正常
```

### 清理环境进度
- 停止运行中的容器
- 删除相关容器
- 释放占用端口
- 准备部署环境

## 🛠️ 集成脚本

### koishi-docker-deploy.sh (完整部署)
```bash
# 使用方法
chmod +x koishi-docker-deploy.sh
sudo ./koishi-docker-deploy.sh

# 执行流程
1. 检查权限
2. 选择端口
3. 检查系统
4. 安装 Docker
5. 🆕 检测现有容器 → 交互式管理
6. 创建数据目录
7. 部署 Koishi
8. 配置防火墙
9. 验证部署
```

### koishi-quick-deploy.sh (快速部署)
```bash
# 使用方法
chmod +x koishi-quick-deploy.sh
./koishi-quick-deploy.sh 8080

# 执行流程
1. 解析参数
2. 选择端口
3. 检查 Docker
4. 🆕 检测现有容器 → 交互式管理
5. 快速部署
```

## 🎭 演示脚本

### container-management-demo.sh
```bash
# 体验容器管理功能
chmod +x container-management-demo.sh
./container-management-demo.sh
```

演示内容：
- 创建测试容器模拟现有环境
- 展示检测功能
- 体验交互式菜单
- 演示重启和管理操作

## 🔧 技术实现

### 容器检测逻辑
```bash
# 检测现有容器
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "koishi|5140"

# 检测端口占用
netstat -tuln | grep ":5140 "

# 检测特定容器
docker ps -a -q -f name="koishi-bot-5140"
```

### 智能处理机制
- **无冲突**: 直接继续部署
- **有冲突**: 显示交互式菜单
- **重启成功**: 显示访问信息并退出
- **重新部署**: 清理环境后继续部署

## 🎯 使用场景

### 场景1: 首次部署
- 检测结果: 无现有容器
- 处理方式: 直接继续部署
- 用户体验: 无感知，流程顺畅

### 场景2: 容器已存在且运行正常
- 检测结果: 发现目标容器
- 推荐操作: 选择重启现有容器
- 用户体验: 快速重启，立即可用

### 场景3: 容器存在但有问题
- 检测结果: 发现容器但服务异常
- 推荐操作: 选择重新部署
- 用户体验: 清理环境，全新部署

### 场景4: 端口被其他服务占用
- 检测结果: 端口冲突
- 推荐操作: 查看状态后决定
- 用户体验: 明确冲突原因，自主选择

## 🚀 优势特点

- ✅ **智能检测**: 全面检测容器和端口状态
- ✅ **交互友好**: 清晰的选项和说明
- ✅ **进度可视**: 所有操作都有进度显示
- ✅ **安全可靠**: 用户确认后才执行操作
- ✅ **灵活处理**: 支持多种处理方式
- ✅ **信息丰富**: 详细的状态和日志显示

## 📝 注意事项

1. **权限要求**: 需要 Docker 操作权限
2. **网络检测**: 需要 `netstat` 命令支持
3. **容器命名**: 遵循 `koishi-bot-{端口}` 命名规范
4. **数据安全**: 重新部署会保留数据目录
5. **服务验证**: 重启后会自动验证服务可用性

这个功能大大提升了 Koishi Docker 部署的用户体验和可靠性！
