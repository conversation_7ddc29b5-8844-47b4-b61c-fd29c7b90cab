#!/bin/bash

# pixluna 插件错误修复应用脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

show_header() {
    echo
    echo -e "${GREEN}========================================"
    echo "    pixluna 插件错误修复工具"
    echo "========================================"
    echo -e "${NC}"
}

# 检查环境
check_environment() {
    log "INFO" "检查环境..."
    
    # 检查是否在正确的目录
    if [ ! -f "package.json" ]; then
        log "ERROR" "未找到 package.json，请在 Koishi 项目根目录运行"
        exit 1
    fi
    
    # 检查 pixluna 插件
    if [ ! -d "node_modules/koishi-plugin-pixluna" ]; then
        log "ERROR" "未找到 pixluna 插件，请先安装"
        exit 1
    fi
    
    log "SUCCESS" "环境检查通过"
}

# 备份原文件
backup_files() {
    log "INFO" "备份原始文件..."
    
    local backup_dir="pixluna-backup-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份关键文件
    if [ -f "node_modules/koishi-plugin-pixluna/lib/utils/request.js" ]; then
        cp "node_modules/koishi-plugin-pixluna/lib/utils/request.js" "$backup_dir/"
    fi
    
    if [ -f "node_modules/koishi-plugin-pixluna/lib/utils/recall.js" ]; then
        cp "node_modules/koishi-plugin-pixluna/lib/utils/recall.js" "$backup_dir/"
    fi
    
    if [ -f "koishi.yml" ]; then
        cp "koishi.yml" "$backup_dir/"
    fi
    
    log "SUCCESS" "文件已备份到: $backup_dir"
}

# 应用配置修复
apply_config_fix() {
    log "INFO" "应用配置修复..."
    
    # 检查当前配置
    if [ -f "koishi.yml" ]; then
        log "INFO" "检测到现有配置文件"
        
        # 备份当前配置
        cp "koishi.yml" "koishi.yml.backup"
        
        # 应用关键修复
        log "FIX" "修复 pixluna 配置..."
        
        # 使用 sed 修改配置（简化版本）
        if grep -q "pixluna:" "koishi.yml"; then
            # 禁用自动撤回
            sed -i 's/enable: true/enable: false/g' koishi.yml 2>/dev/null || true
            
            # 启用压缩
            sed -i 's/compress: false/compress: true/g' koishi.yml 2>/dev/null || true
            
            # 启用日志
            sed -i 's/isLog: false/isLog: true/g' koishi.yml 2>/dev/null || true
            
            log "SUCCESS" "配置修复完成"
        else
            log "WARN" "未找到 pixluna 配置，请手动添加"
        fi
    else
        log "WARN" "未找到配置文件，将创建优化配置"
        
        # 复制优化配置
        if [ -f "pixluna-fixes/optimized-config.yml" ]; then
            cp "pixluna-fixes/optimized-config.yml" "koishi.yml"
            log "SUCCESS" "已创建优化配置文件"
        fi
    fi
}

# 应用代码修复
apply_code_fix() {
    log "INFO" "应用代码修复..."
    
    # 检查是否有源码版本
    if [ -d "C:/Users/<USER>/Desktop/q/pixluna-main" ]; then
        log "INFO" "检测到源码版本，建议重新编译"
        
        read -p "是否要应用源码修复并重新编译? (y/N): " apply_source
        
        if [[ $apply_source == [yY] ]]; then
            log "FIX" "应用源码修复..."
            
            # 复制修复文件
            if [ -f "pixluna-fixes/request-fix.ts" ]; then
                cp "pixluna-fixes/request-fix.ts" "C:/Users/<USER>/Desktop/q/pixluna-main/src/utils/request.ts"
                log "SUCCESS" "request.ts 修复已应用"
            fi
            
            if [ -f "pixluna-fixes/recall-fix.ts" ]; then
                cp "pixluna-fixes/recall-fix.ts" "C:/Users/<USER>/Desktop/q/pixluna-main/src/utils/recall.ts"
                log "SUCCESS" "recall.ts 修复已应用"
            fi
            
            # 重新编译
            log "INFO" "重新编译插件..."
            cd "C:/Users/<USER>/Desktop/q/pixluna-main"
            
            if command -v npm >/dev/null 2>&1; then
                npm run build >/dev/null 2>&1
                if [ $? -eq 0 ]; then
                    log "SUCCESS" "插件编译成功"
                    
                    # 复制编译结果
                    if [ -d "lib" ]; then
                        cp -r lib/* "../node_modules/koishi-plugin-pixluna/lib/"
                        log "SUCCESS" "修复文件已部署"
                    fi
                else
                    log "ERROR" "插件编译失败"
                fi
            else
                log "ERROR" "未找到 npm，无法编译"
            fi
            
            cd - >/dev/null
        fi
    else
        log "WARN" "未找到源码，跳过代码修复"
    fi
}

# 重启服务
restart_service() {
    log "INFO" "准备重启 Koishi 服务..."
    
    read -p "是否要重启 Koishi 服务以应用修复? (y/N): " restart
    
    if [[ $restart == [yY] ]]; then
        log "FIX" "重启服务..."
        
        # 查找并停止现有进程
        pkill -f "koishi" 2>/dev/null || true
        pkill -f "npm.*start" 2>/dev/null || true
        
        sleep 2
        
        # 启动服务
        log "INFO" "启动 Koishi..."
        nohup npm start > koishi.log 2>&1 &
        
        log "SUCCESS" "服务已重启"
        log "INFO" "请查看 koishi.log 了解运行状态"
    fi
}

# 显示修复说明
show_fix_summary() {
    echo
    log "SUCCESS" "修复应用完成！"
    echo
    echo -e "${BLUE}已应用的修复：${NC}"
    echo "✅ 禁用自动撤回功能（避免错误代码 11255）"
    echo "✅ 启用图片压缩（提高成功率）"
    echo "✅ 启用详细日志（便于调试）"
    echo "✅ 优化网络请求（添加重试机制）"
    echo "✅ 改善错误处理（更好的错误信息）"
    echo
    echo -e "${YELLOW}建议的后续操作：${NC}"
    echo "1. 检查 koishi.yml 配置是否正确"
    echo "2. 重启 Koishi 服务"
    echo "3. 测试 pixluna 命令"
    echo "4. 观察日志输出"
    echo "5. 如有问题，查看详细日志"
    echo
    echo -e "${BLUE}测试命令：${NC}"
    echo "pixluna          # 基础测试"
    echo "pixluna 风景     # 带标签测试"
    echo "pixluna -n 2     # 多图测试"
    echo
    echo -e "${GREEN}修复完成！${NC}"
}

# 主函数
main() {
    show_header
    
    log "INFO" "开始应用 pixluna 插件修复..."
    echo
    
    check_environment
    backup_files
    apply_config_fix
    apply_code_fix
    restart_service
    show_fix_summary
}

main "$@"
