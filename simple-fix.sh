#!/bin/bash

# 简化的 Koishi 修复脚本
echo "========================================"
echo "    Koishi 简化修复工具"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

# 步骤1：停止现有进程
log "INFO" "步骤1：停止现有进程..."
pkill -f "npm.*start" 2>/dev/null || true
pkill -f "koishi" 2>/dev/null || true
log "SUCCESS" "进程停止完成"
echo

# 步骤2：安装编译工具
log "INFO" "步骤2：安装编译工具..."
if ! command -v gcc >/dev/null 2>&1; then
    log "FIX" "安装编译工具..."
    sudo apt update >/dev/null 2>&1
    sudo apt install -y build-essential python3 make g++ python3-dev >/dev/null 2>&1
    log "SUCCESS" "编译工具安装完成"
else
    log "SUCCESS" "编译工具已存在"
fi
echo

# 步骤3：检查项目目录
log "INFO" "步骤3：检查项目目录..."
if [ ! -f "package.json" ]; then
    log "ERROR" "未找到 package.json，请确保在正确的项目目录中运行"
    exit 1
fi
log "SUCCESS" "项目目录检查完成"
echo

# 步骤4：重新安装依赖
log "INFO" "步骤4：重新安装依赖..."
log "FIX" "清理旧的依赖..."
rm -rf node_modules package-lock.json 2>/dev/null || true
npm cache clean --force >/dev/null 2>&1 || true

log "FIX" "安装依赖包..."
npm install >/dev/null 2>&1
if [ $? -eq 0 ]; then
    log "SUCCESS" "依赖安装完成"
else
    log "WARN" "依赖安装可能有问题，但继续执行..."
fi
echo

# 步骤5：配置防火墙
log "INFO" "步骤5：配置防火墙..."
if command -v ufw >/dev/null 2>&1; then
    sudo ufw allow 5140 >/dev/null 2>&1 || true
    log "SUCCESS" "UFW 防火墙配置完成"
elif command -v iptables >/dev/null 2>&1; then
    sudo iptables -A INPUT -p tcp --dport 5140 -j ACCEPT >/dev/null 2>&1 || true
    log "SUCCESS" "iptables 防火墙配置完成"
else
    log "WARN" "未找到防火墙工具"
fi
echo

# 步骤6：启动 Koishi
log "INFO" "步骤6：启动 Koishi..."

# 创建日志目录
mkdir -p logs

# 启动服务
log "FIX" "启动 Koishi 服务..."
nohup npm start > logs/koishi.log 2>&1 &
KOISHI_PID=$!

# 保存 PID
echo $KOISHI_PID > koishi.pid
log "SUCCESS" "Koishi 已启动 (PID: $KOISHI_PID)"
echo

# 步骤7：验证访问
log "INFO" "步骤7：等待服务启动..."
sleep 10

log "INFO" "验证服务访问..."
for i in {1..5}; do
    log "INFO" "尝试访问 ($i/5)..."
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:5140 2>/dev/null | grep -q "200"; then
        log "SUCCESS" "Koishi 服务访问正常！"
        break
    elif [ $i -eq 5 ]; then
        log "WARN" "访问验证失败，但服务可能已启动"
    else
        sleep 5
    fi
done

echo
echo -e "${GREEN}🎉 修复完成！${NC}"
echo
echo -e "${BLUE}访问信息：${NC}"
echo "  本地访问: http://localhost:5140"
echo "  外部访问: http://$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP"):5140"
echo
echo -e "${BLUE}管理命令：${NC}"
echo "  查看日志: tail -f logs/koishi.log"
echo "  停止服务: kill \$(cat koishi.pid)"
echo "  重启服务: ./simple-fix.sh"
echo
echo -e "${YELLOW}注意事项：${NC}"
echo "• 如果使用云服务器，请确保安全组开放 5140 端口"
echo "• 首次访问可能需要几分钟初始化"
echo

log "SUCCESS" "修复脚本执行完成！"
