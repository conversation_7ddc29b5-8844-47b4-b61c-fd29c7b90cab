#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机回复QQ机器人
专门用于随机回复"你好"等消息
"""

import asyncio
import logging
import traceback
import random
from datetime import datetime

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class RandomReplyBot(botpy.Client):
    """随机回复QQ机器人"""
    
    def __init__(self):
        # 设置事件订阅
        intents = botpy.Intents(
            public_guild_messages=True,  # 公域消息事件
            direct_message=True,  # 私信事件
        )
        
        super().__init__(intents=intents)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='[%(levelname)s] %(asctime)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/bot.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 随机回复库
        self.hello_replies = [
            "你好！很高兴见到你！😊",
            "嗨！今天过得怎么样？🌟",
            "你好呀！有什么我可以帮助你的吗？😄",
            "Hello！欢迎来聊天！👋",
            "你好！我是QQ机器人，很开心认识你！🤖",
            "嗨嗨！今天天气不错呢～☀️",
            "你好！感谢你和我打招呼！💕",
            "Hello World！程序员的经典问候！💻",
            "你好！要不要聊聊天？😊",
            "嗨！我正在等你呢！✨",
            "你好！今天是美好的一天！🌈",
            "Hello！很高兴在这里遇见你！🎉",
            "你好！有什么有趣的事情想分享吗？😄",
            "嗨！我刚刚在想你呢！💭",
            "你好！准备好开始愉快的聊天了吗？🎈",
            "Hello！你的到来让我很开心！🌸",
            "你好！今天是个聊天的好日子！☀️",
            "嗨！我一直在等待和你的对话！⭐",
            "你好！让我们开始一段有趣的聊天吧！🎪",
            "Hello！很荣幸能和你交流！🎭"
        ]
        
        self.bye_replies = [
            "再见！期待下次聊天！👋",
            "拜拜！记得想我哦～😘",
            "再见！愿你每天都开心！🌟",
            "Bye bye！下次见！✨",
            "再见！保重身体！💪",
            "拜拜！期待我们的下次相遇！🤗",
            "再见！今天聊得很开心！😊",
            "拜拜！记得常来找我玩！🎮",
            "再见！祝你有美好的一天！🌈",
            "Goodbye！愿你一切顺利！🍀"
        ]
        
        self.thank_replies = [
            "不客气！很高兴能帮到你！😄",
            "不用谢！这是我应该做的！😊",
            "客气什么！我们是朋友嘛！🤝",
            "不客气！有需要随时找我！💪",
            "谢谢你的感谢！😄",
            "能帮到你我很开心！🌟",
            "不用客气！互相帮助嘛！🤗",
            "这是我的荣幸！😊",
            "帮助你是我的快乐！🎉"
        ]
        
        self.default_replies = [
            "我收到了你的消息！😊",
            "有趣！告诉我更多吧！😄",
            "我在认真听呢！继续说～👂",
            "这个话题很有意思！💭",
            "我正在学习中，谢谢你的分享！📚",
            "哇，真的吗？听起来很棒！✨",
            "继续和我聊天吧！我很喜欢！😊",
            "你说的很有道理！👍",
            "我觉得你很有趣！😄",
            "让我们继续这个话题！💬"
        ]

    async def on_ready(self):
        """机器人启动完成事件"""
        self.logger.info(f"随机回复机器人 {self.robot.name} 已上线！")
        print("=" * 60)
        print(f"🎉 随机回复机器人启动成功！")
        print(f"🤖 机器人名称: {self.robot.name}")
        print(f"🆔 机器人ID: {self.robot.id}")
        print(f"📱 机器人QQ号: 3889706980")
        print("=" * 60)
        print("✅ 机器人已准备就绪，等待消息...")
        print("💡 在QQ频道中@机器人发送'你好'试试随机回复功能！")
        print("🎲 每次回复都会从多个选项中随机选择")
        print("=" * 60)

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息"""
        try:
            self.logger.info(f"收到@消息: {message.content}")
            print(f"📨 收到@消息: {message.content}")
            
            # 提取消息内容
            content = self.extract_content(message.content)
            print(f"💭 处理内容: {content}")
            
            # 生成随机回复
            reply = self.generate_random_reply(content)
            print(f"🎲 随机选择回复: {reply}")
            
            # 发送回复
            await self.send_message(message, reply)
                
        except Exception as e:
            self.logger.error(f"处理@消息时发生错误: {e}")
            print(f"❌ 处理消息时发生错误: {e}")
            await self.send_error_message(message)

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容，去除@机器人部分"""
        import re
        content = re.sub(r'<@!\d+>\s*', '', raw_content).strip()
        return content

    def generate_random_reply(self, content: str) -> str:
        """生成随机回复"""
        content_lower = content.lower()
        
        # 根据关键词选择对应的回复库
        if any(word in content_lower for word in ["你好", "hello", "hi", "嗨", "您好"]):
            selected_reply = random.choice(self.hello_replies)
            print(f"🎯 匹配到'你好'关键词，从{len(self.hello_replies)}个回复中随机选择")
            return selected_reply
            
        elif any(word in content_lower for word in ["再见", "bye", "拜拜", "goodbye"]):
            selected_reply = random.choice(self.bye_replies)
            print(f"🎯 匹配到'再见'关键词，从{len(self.bye_replies)}个回复中随机选择")
            return selected_reply
            
        elif any(word in content_lower for word in ["谢谢", "thank", "感谢", "thanks"]):
            selected_reply = random.choice(self.thank_replies)
            print(f"🎯 匹配到'谢谢'关键词，从{len(self.thank_replies)}个回复中随机选择")
            return selected_reply
            
        else:
            selected_reply = random.choice(self.default_replies)
            print(f"🎯 使用默认回复，从{len(self.default_replies)}个回复中随机选择")
            return selected_reply

    async def send_message(self, original_message: Message, content: str):
        """发送消息"""
        try:
            await self.api.post_message(
                channel_id=original_message.channel_id,
                content=content,
                msg_id=original_message.id
            )
            self.logger.info(f"发送消息成功: {content}")
            print(f"✅ 消息发送成功")
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            print(f"❌ 发送消息失败: {e}")

    async def send_error_message(self, message: Message):
        """发送错误消息"""
        error_replies = [
            "❌ 哎呀，出了点小问题，请稍后重试",
            "❌ 系统忙碌中，请稍等片刻",
            "❌ 遇到了一点技术问题，马上就好"
        ]
        await self.send_message(message, random.choice(error_replies))


def main():
    """主函数"""
    print("🎲 正在启动随机回复QQ机器人...")
    print("=" * 60)
    print(f"📱 AppID: {BOT_CONFIG['appid']}")
    print(f"🔑 使用已验证的AppSecret")
    print("🎯 特色功能: 随机回复")
    print("=" * 60)
    
    # 创建机器人实例
    bot = RandomReplyBot()
    
    try:
        print("🔄 使用AppSecret启动机器人...")
        bot.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 详细错误信息:")
        traceback.print_exc()


if __name__ == "__main__":
    main()
