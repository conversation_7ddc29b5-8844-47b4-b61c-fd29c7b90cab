#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能回复QQ机器人
无需@，自动识别关键词并随机回复
"""

import asyncio
import logging
import traceback
import random
from datetime import datetime

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class SmartReplyBot(botpy.Client):
    """智能回复QQ机器人 - 无需@自动识别回复"""
    
    def __init__(self):
        # 设置事件订阅 - 订阅所有消息事件
        intents = botpy.Intents(
            public_guild_messages=True,  # 公域消息事件
            direct_message=True,  # 私信事件
            guild_messages=True,  # 频道消息事件（重要：接收所有消息）
            guilds=True,  # 频道事件
        )
        
        super().__init__(intents=intents)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='[%(levelname)s] %(asctime)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/smart_bot.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 随机回复库
        self.hello_replies = [
            "你好！很高兴见到你！😊",
            "嗨！今天过得怎么样？🌟",
            "你好呀！有什么我可以帮助你的吗？😄",
            "Hello！欢迎来聊天！👋",
            "你好！我是QQ机器人，很开心认识你！🤖",
            "嗨嗨！今天天气不错呢～☀️",
            "你好！感谢你和我打招呼！💕",
            "Hello World！程序员的经典问候！💻",
            "你好！要不要聊聊天？😊",
            "嗨！我正在等你呢！✨",
            "你好！今天是美好的一天！🌈",
            "Hello！很高兴在这里遇见你！🎉"
        ]
        
        self.bye_replies = [
            "再见！期待下次聊天！👋",
            "拜拜！记得想我哦～😘",
            "再见！愿你每天都开心！🌟",
            "Bye bye！下次见！✨",
            "再见！保重身体！💪",
            "拜拜！期待我们的下次相遇！🤗"
        ]
        
        self.thank_replies = [
            "不客气！很高兴能帮到你！😄",
            "不用谢！这是我应该做的！😊",
            "客气什么！我们是朋友嘛！🤝",
            "不客气！有需要随时找我！💪",
            "谢谢你的感谢！😄",
            "能帮到你我很开心！🌟"
        ]

    async def on_ready(self):
        """机器人启动完成事件"""
        self.logger.info(f"智能回复机器人 {self.robot.name} 已上线！")
        print("=" * 60)
        print(f"🎉 智能回复机器人启动成功！")
        print(f"🤖 机器人名称: {self.robot.name}")
        print(f"🆔 机器人ID: {self.robot.id}")
        print("=" * 60)
        print("✅ 机器人已准备就绪，等待消息...")
        print("🎯 特色功能: 无需@，自动识别关键词回复")
        print("🔍 支持关键词: 你好、再见、谢谢、机器人、时间等")
        print("=" * 60)

    async def on_message_create(self, message: Message):
        """处理所有消息 - 智能识别关键词自动回复"""
        try:
            # 检查是否是机器人自己发的消息
            if message.author.bot:
                return
            
            content = message.content.strip()
            if not content:
                return
                
            self.logger.info(f"收到消息: {content} (来自: {message.author.username})")
            print(f"💬 收到消息: {content}")
            
            # 智能关键词检测
            reply = self.smart_keyword_detection(content)
            
            if reply:
                print(f"🎯 检测到关键词，准备回复: {reply}")
                await self.send_message(message, reply)
            else:
                print(f"ℹ️ 未检测到关键词，不回复")
                
        except Exception as e:
            self.logger.error(f"处理消息时发生错误: {e}")
            print(f"❌ 处理消息时发生错误: {e}")

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息"""
        try:
            self.logger.info(f"收到@消息: {message.content}")
            print(f"📨 收到@消息: {message.content}")
            
            # 提取消息内容
            content = self.extract_content(message.content)
            print(f"💭 处理内容: {content}")
            
            # 对@消息总是回复
            reply = self.smart_keyword_detection(content)
            if not reply:
                reply = "我收到了你的@消息！😊 有什么可以帮助你的吗？"
            
            await self.send_message(message, reply)
                
        except Exception as e:
            self.logger.error(f"处理@消息时发生错误: {e}")
            print(f"❌ 处理消息时发生错误: {e}")

    def smart_keyword_detection(self, content: str) -> str:
        """智能关键词检测和回复生成"""
        content_lower = content.lower()
        
        # 问候检测
        if any(word in content_lower for word in ["你好", "hello", "hi", "嗨", "您好", "早上好", "晚上好"]):
            selected_reply = random.choice(self.hello_replies)
            print(f"🎯 匹配到'问候'关键词，从{len(self.hello_replies)}个回复中随机选择")
            return selected_reply
            
        # 告别检测
        elif any(word in content_lower for word in ["再见", "bye", "拜拜", "goodbye", "晚安"]):
            selected_reply = random.choice(self.bye_replies)
            print(f"🎯 匹配到'告别'关键词，从{len(self.bye_replies)}个回复中随机选择")
            return selected_reply
            
        # 感谢检测
        elif any(word in content_lower for word in ["谢谢", "thank", "感谢", "thanks", "谢了"]):
            selected_reply = random.choice(self.thank_replies)
            print(f"🎯 匹配到'感谢'关键词，从{len(self.thank_replies)}个回复中随机选择")
            return selected_reply
            
        # 机器人相关
        elif any(word in content_lower for word in ["机器人", "bot", "助手", "ai", "人工智能"]):
            bot_replies = [
                "是的，我是QQ机器人！🤖 很高兴为你服务！",
                "没错！我是智能助手！🤖✨ 有什么需要帮助的吗？",
                "对呀！我是用Python开发的QQ机器人！💻",
                "我是你的专属AI助手！🤖 随时为你服务！"
            ]
            selected_reply = random.choice(bot_replies)
            print(f"🎯 匹配到'机器人'关键词")
            return selected_reply
            
        # 时间相关
        elif any(word in content_lower for word in ["时间", "几点", "现在", "当前时间"]):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            time_replies = [
                f"🕐 现在是: {current_time}",
                f"⏰ 当前时间: {current_time}\n时间过得真快呢！",
                f"🕐 时间显示: {current_time}\n要珍惜时间哦！"
            ]
            selected_reply = random.choice(time_replies)
            print(f"🎯 匹配到'时间'关键词")
            return selected_reply
            
        # 天气相关
        elif any(word in content_lower for word in ["天气", "下雨", "晴天", "阴天"]):
            weather_replies = [
                "我暂时还不能查询天气呢！😅 不过今天心情很好！☀️",
                "天气怎么样呢？我在室内感觉不到～😄",
                "无论什么天气，和你聊天都很开心！🌈"
            ]
            selected_reply = random.choice(weather_replies)
            print(f"🎯 匹配到'天气'关键词")
            return selected_reply
            
        # 没有匹配到关键词
        else:
            print(f"ℹ️ 未匹配到关键词")
            return None

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容，去除@机器人部分"""
        import re
        content = re.sub(r'<@!\d+>\s*', '', raw_content).strip()
        return content

    async def send_message(self, original_message: Message, content: str):
        """发送消息"""
        try:
            await self.api.post_message(
                channel_id=original_message.channel_id,
                content=content,
                msg_id=original_message.id
            )
            self.logger.info(f"发送消息成功: {content}")
            print(f"✅ 消息发送成功")
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            print(f"❌ 发送消息失败: {e}")


def main():
    """主函数"""
    print("🧠 正在启动智能回复QQ机器人...")
    print("=" * 60)
    print(f"📱 AppID: {BOT_CONFIG['appid']}")
    print(f"🔑 使用已验证的AppSecret")
    print("🎯 特色功能: 智能关键词识别，无需@自动回复")
    print("=" * 60)
    
    # 创建机器人实例
    bot = SmartReplyBot()
    
    try:
        print("🔄 使用AppSecret启动机器人...")
        bot.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 详细错误信息:")
        traceback.print_exc()


if __name__ == "__main__":
    main()
