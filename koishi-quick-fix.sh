#!/bin/bash

#==============================================================================
# Koishi 快速修复脚本
# 
# 专门用于修复安装过程中遇到的常见问题
# 
# 使用方法：
#   ./koishi-quick-fix.sh
#
# 版本：v1.0.0
#==============================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    
    case "$level" in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
    esac
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 修复 APT 问题并安装基础依赖
fix_and_install_dependencies() {
    log "INFO" "开始修复并安装基础依赖..."
    
    # 1. 修复 APT 锁定问题
    log "INFO" "修复 APT 锁定问题..."
    sudo killall apt apt-get dpkg >/dev/null 2>&1 || true
    sudo rm -f /var/lib/dpkg/lock-frontend /var/lib/dpkg/lock /var/cache/apt/archives/lock
    
    # 2. 重新配置 dpkg
    log "INFO" "重新配置 dpkg..."
    sudo dpkg --configure -a
    
    # 3. 修复损坏的包
    log "INFO" "修复损坏的包..."
    sudo apt --fix-broken install -y
    
    # 4. 更新包缓存
    log "INFO" "更新包缓存..."
    sudo apt update
    
    # 5. 安装基础依赖包
    log "INFO" "安装基础依赖包..."
    local base_packages="curl wget git unzip ca-certificates gnupg lsb-release"
    
    if sudo apt install -y $base_packages; then
        log "SUCCESS" "基础依赖包安装成功"
    else
        log "ERROR" "基础依赖包安装失败"
        return 1
    fi
    
    # 6. 安装开发工具
    log "INFO" "安装开发工具..."
    local dev_packages="build-essential python3 python3-pip make g++"
    
    if sudo apt install -y $dev_packages; then
        log "SUCCESS" "开发工具安装成功"
    else
        log "WARNING" "开发工具安装失败，某些插件可能无法编译"
    fi
    
    return 0
}

# 安装 Node.js
install_nodejs() {
    log "INFO" "安装 Node.js..."
    
    # 检查是否已安装
    if command_exists node; then
        local version=$(node --version | sed 's/v//')
        local major=$(echo "$version" | cut -d. -f1)
        
        if [ "$major" -ge 18 ]; then
            log "SUCCESS" "Node.js 已安装且版本符合要求: v$version"
            return 0
        else
            log "WARNING" "Node.js 版本过低: v$version，需要升级"
        fi
    fi
    
    # 添加 NodeSource 仓库
    log "INFO" "添加 NodeSource 仓库..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
    
    # 安装 Node.js
    log "INFO" "安装 Node.js..."
    if sudo apt-get install -y nodejs; then
        log "SUCCESS" "Node.js 安装成功"
        log "INFO" "Node.js 版本: $(node --version)"
        log "INFO" "npm 版本: $(npm --version)"
        return 0
    else
        log "ERROR" "Node.js 安装失败"
        return 1
    fi
}

# 配置 npm
configure_npm() {
    log "INFO" "配置 npm..."
    
    # 配置全局目录
    local npm_global="$HOME/.npm-global"
    mkdir -p "$npm_global"
    npm config set prefix "$npm_global"
    
    # 添加到 PATH
    if ! grep -q "$npm_global/bin" "$HOME/.bashrc" 2>/dev/null; then
        echo "export PATH=$npm_global/bin:\$PATH" >> "$HOME/.bashrc"
        export PATH="$npm_global/bin:$PATH"
        log "SUCCESS" "已添加 npm 全局目录到 PATH"
    fi
    
    # 询问是否使用国内镜像源
    read -p "是否配置国内镜像源？(y/N): " -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        npm config set registry https://registry.npmmirror.com
        log "SUCCESS" "已配置 npm 国内镜像源"
    else
        npm config set registry https://registry.npmjs.org/
        log "SUCCESS" "使用 npm 官方镜像源"
    fi
    
    log "SUCCESS" "npm 配置完成"
}

# 创建简单的 Koishi 项目
create_simple_project() {
    log "INFO" "创建简单的 Koishi 项目..."
    
    local project_name="koishi-bot"
    local project_path="$HOME/$project_name"
    
    # 检查目录是否存在
    if [ -d "$project_path" ]; then
        log "WARNING" "目录 $project_path 已存在"
        read -p "是否删除并重新创建？(y/N): " -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -rf "$project_path"
        else
            log "INFO" "使用现有目录"
        fi
    fi
    
    # 创建项目目录
    mkdir -p "$project_path"
    cd "$project_path"
    
    # 初始化 package.json
    log "INFO" "初始化项目..."
    cat > package.json << EOF
{
  "name": "$project_name",
  "version": "1.0.0",
  "description": "Koishi Bot Project",
  "main": "index.js",
  "scripts": {
    "start": "koishi start",
    "dev": "koishi start --watch"
  },
  "keywords": ["koishi", "bot"],
  "license": "MIT"
}
EOF
    
    # 安装 Koishi
    log "INFO" "安装 Koishi 核心包..."
    if npm install koishi @koishijs/plugin-console @koishijs/plugin-dataview; then
        log "SUCCESS" "Koishi 安装成功"
    else
        log "ERROR" "Koishi 安装失败"
        return 1
    fi
    
    # 创建配置文件
    log "INFO" "创建配置文件..."
    cat > koishi.yml << EOF
host: 0.0.0.0
port: 5140

database:
  type: sqlite
  path: ./data/koishi.db

plugins:
  console:
    open: false
  dataview: {}

logger:
  levels:
    base: 2
EOF
    
    # 创建必要目录
    mkdir -p data logs
    
    # 创建启动脚本
    cat > start.sh << 'EOF'
#!/bin/bash
echo "启动 Koishi..."
npm start
EOF
    chmod +x start.sh
    
    log "SUCCESS" "项目创建完成: $project_path"
    return 0
}

# 测试安装结果
test_installation() {
    log "INFO" "测试安装结果..."
    
    # 检查 Node.js
    if command_exists node; then
        log "SUCCESS" "Node.js: $(node --version)"
    else
        log "ERROR" "Node.js 未正确安装"
        return 1
    fi
    
    # 检查 npm
    if command_exists npm; then
        log "SUCCESS" "npm: $(npm --version)"
    else
        log "ERROR" "npm 未正确安装"
        return 1
    fi
    
    # 检查项目目录
    if [ -d "$HOME/koishi-bot" ]; then
        log "SUCCESS" "项目目录存在"
    else
        log "ERROR" "项目目录不存在"
        return 1
    fi
    
    # 检查 Koishi 安装
    cd "$HOME/koishi-bot"
    if [ -f "package.json" ] && [ -d "node_modules" ]; then
        log "SUCCESS" "Koishi 项目完整"
    else
        log "ERROR" "Koishi 项目不完整"
        return 1
    fi
    
    log "SUCCESS" "所有检查通过！"
    return 0
}

# 显示完成信息
show_completion() {
    echo
    echo -e "${GREEN}🎉 Koishi 快速修复和安装完成！${NC}"
    echo
    echo -e "${BLUE}项目路径：${NC}$HOME/koishi-bot"
    echo -e "${BLUE}启动命令：${NC}"
    echo "  cd $HOME/koishi-bot"
    echo "  npm start"
    echo "  # 或者"
    echo "  ./start.sh"
    echo
    echo -e "${BLUE}访问地址：${NC}http://localhost:5140"
    echo
    echo -e "${YELLOW}重要提示：${NC}"
    echo "• 首次启动需要几分钟初始化"
    echo "• 访问控制台配置机器人账号"
    echo "• 如需重新加载环境变量，请运行: source ~/.bashrc"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}"
    echo "========================================"
    echo "    Koishi 快速修复脚本 v1.0.0"
    echo "========================================"
    echo -e "${NC}"
    
    log "INFO" "开始快速修复和安装..."
    
    # 执行修复和安装步骤
    if fix_and_install_dependencies; then
        log "SUCCESS" "基础依赖修复完成"
    else
        log "ERROR" "基础依赖修复失败"
        exit 1
    fi
    
    if install_nodejs; then
        log "SUCCESS" "Node.js 安装完成"
    else
        log "ERROR" "Node.js 安装失败"
        exit 1
    fi
    
    configure_npm
    
    if create_simple_project; then
        log "SUCCESS" "项目创建完成"
    else
        log "ERROR" "项目创建失败"
        exit 1
    fi
    
    if test_installation; then
        show_completion
        log "SUCCESS" "快速修复和安装完成！"
    else
        log "ERROR" "安装验证失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
