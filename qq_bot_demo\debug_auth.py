#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ机器人认证调试脚本
用于测试不同的认证方式
"""

import asyncio
import aiohttp
import json
from config import BOT_CONFIG


async def test_get_access_token():
    """测试获取access_token"""
    print("🔍 测试获取access_token...")
    
    url = "https://bots.qq.com/app/getAppAccessToken"
    
    # 测试不同的参数组合
    test_configs = [
        {
            "name": "使用AppID + AppSecret",
            "data": {
                "appId": BOT_CONFIG["appid"],
                "clientSecret": BOT_CONFIG["secret"]
            }
        },
        {
            "name": "使用AppID + Token",
            "data": {
                "appId": BOT_CONFIG["appid"],
                "clientSecret": BOT_CONFIG["token"]
            }
        }
    ]
    
    async with aiohttp.ClientSession() as session:
        for config in test_configs:
            print(f"\n📋 {config['name']}:")
            print(f"   AppID: {config['data']['appId']}")
            print(f"   Secret: {config['data']['clientSecret'][:10]}...")
            
            try:
                async with session.post(url, json=config['data']) as response:
                    result = await response.json()
                    print(f"   状态码: {response.status}")
                    print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    
                    if response.status == 200 and "access_token" in result:
                        print("   ✅ 获取access_token成功！")
                        return result["access_token"]
                    else:
                        print("   ❌ 获取access_token失败")
                        
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
    
    return None


async def test_bot_info(access_token):
    """测试获取机器人信息"""
    if not access_token:
        print("❌ 没有有效的access_token，跳过机器人信息测试")
        return
    
    print(f"\n🤖 测试获取机器人信息...")
    print(f"   使用access_token: {access_token[:20]}...")
    
    url = "https://api.sgroup.qq.com/users/@me"
    headers = {
        "Authorization": f"Bot {BOT_CONFIG['appid']}.{access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                result = await response.text()
                print(f"   状态码: {response.status}")
                print(f"   响应: {result}")
                
                if response.status == 200:
                    print("   ✅ 获取机器人信息成功！")
                else:
                    print("   ❌ 获取机器人信息失败")
                    
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")


def check_config_format():
    """检查配置格式"""
    print("🔍 检查配置格式...")
    print("=" * 50)
    
    # 检查AppID
    appid = BOT_CONFIG["appid"]
    print(f"AppID: {appid}")
    print(f"   类型: {type(appid)}")
    print(f"   长度: {len(str(appid))}")
    print(f"   是否为数字: {str(appid).isdigit()}")
    
    # 检查Token
    token = BOT_CONFIG["token"]
    print(f"\nToken: {token[:10]}...{token[-4:]}")
    print(f"   类型: {type(token)}")
    print(f"   长度: {len(token)}")
    print(f"   是否包含特殊字符: {any(c in token for c in '!@#$%^&*()+=[]{}|;:,.<>?')}")
    
    # 检查Secret
    secret = BOT_CONFIG["secret"]
    print(f"\nSecret: {secret[:10]}...{secret[-4:]}")
    print(f"   类型: {type(secret)}")
    print(f"   长度: {len(secret)}")
    print(f"   是否包含特殊字符: {any(c in secret for c in '!@#$%^&*()+=[]{}|;:,.<>?')}")
    
    print("=" * 50)


async def main():
    """主函数"""
    print("🔧 QQ机器人认证调试")
    print("=" * 50)
    
    # 检查配置格式
    check_config_format()
    
    # 测试获取access_token
    access_token = await test_get_access_token()
    
    # 测试获取机器人信息
    await test_bot_info(access_token)
    
    print("\n" + "=" * 50)
    print("🎯 调试建议:")
    print("1. 确认在QQ开放平台上机器人状态为'已创建'")
    print("2. 检查AppID、Token、Secret是否从正确位置复制")
    print("3. 确认机器人已通过审核（如果需要）")
    print("4. 尝试重新生成Token和Secret")
    print("5. 检查网络连接和防火墙设置")


if __name__ == "__main__":
    asyncio.run(main())
