import { Context, Schema, Logger, Session } from 'koishi'

export const name = 'example'
export const usage = `
## 功能说明

这是一个功能丰富的 Koishi 插件示例，包含以下功能：

- 基础命令处理
- 配置管理
- 数据库操作
- 事件监听
- 中间件使用
- 国际化支持
- 错误处理

## 使用方法

1. 配置插件参数
2. 使用 \`example.hello\` 命令测试基础功能
3. 使用 \`example.points\` 命令管理积分系统
4. 使用 \`example.random\` 命令生成随机数

## 注意事项

- 积分系统需要数据库支持
- 部分功能需要管理员权限
`

// 插件配置接口
export interface Config {
  prefix: string
  maxMessageLength: number
  enableLogging: boolean
  adminUsers: string[]
  pointsConfig: {
    dailyBonus: number
    maxPoints: number
    enableReset: boolean
  }
}

// 配置模式定义
export const Config: Schema<Config> = Schema.object({
  prefix: Schema.string()
    .default('Bot')
    .description('回复消息的前缀'),
  
  maxMessageLength: Schema.number()
    .min(10)
    .max(2000)
    .default(500)
    .description('最大消息长度限制'),
  
  enableLogging: Schema.boolean()
    .default(true)
    .description('是否启用详细日志记录'),
  
  adminUsers: Schema.array(Schema.string())
    .default([])
    .description('管理员用户ID列表'),
  
  pointsConfig: Schema.object({
    dailyBonus: Schema.number()
      .min(1)
      .max(1000)
      .default(10)
      .description('每日签到奖励积分'),
    
    maxPoints: Schema.number()
      .min(100)
      .max(100000)
      .default(10000)
      .description('积分上限'),
    
    enableReset: Schema.boolean()
      .default(false)
      .description('是否允许重置积分'),
  }).description('积分系统配置'),
})

// 扩展数据库表结构
declare module 'koishi' {
  interface Tables {
    example_users: ExampleUser
    example_logs: ExampleLog
  }
}

export interface ExampleUser {
  id: number
  userId: string
  username: string
  points: number
  lastSignIn: Date
  createdAt: Date
  updatedAt: Date
}

export interface ExampleLog {
  id: number
  userId: string
  action: string
  details: string
  timestamp: Date
}

// 主插件函数
export function apply(ctx: Context, config: Config) {
  const logger = ctx.logger('example')
  
  // 初始化数据库表
  initDatabase(ctx)
  
  // 注册中间件
  registerMiddleware(ctx, config, logger)
  
  // 注册命令
  registerCommands(ctx, config, logger)
  
  // 注册事件监听器
  registerEventListeners(ctx, config, logger)
  
  // 插件启动日志
  if (config.enableLogging) {
    logger.info('Example plugin loaded successfully')
  }
}

// 初始化数据库
function initDatabase(ctx: Context) {
  // 用户表
  ctx.model.extend('example_users', {
    id: 'unsigned',
    userId: 'string',
    username: 'string',
    points: 'integer',
    lastSignIn: 'timestamp',
    createdAt: 'timestamp',
    updatedAt: 'timestamp',
  }, {
    primary: 'id',
    autoInc: true,
    unique: ['userId'],
  })
  
  // 日志表
  ctx.model.extend('example_logs', {
    id: 'unsigned',
    userId: 'string',
    action: 'string',
    details: 'text',
    timestamp: 'timestamp',
  }, {
    primary: 'id',
    autoInc: true,
  })
}

// 注册中间件
function registerMiddleware(ctx: Context, config: Config, logger: Logger) {
  // 消息长度检查中间件
  ctx.middleware((session, next) => {
    if (session.content && session.content.length > config.maxMessageLength) {
      return `消息太长了！最大长度为 ${config.maxMessageLength} 字符。`
    }
    return next()
  })
  
  // 日志记录中间件
  if (config.enableLogging) {
    ctx.middleware((session, next) => {
      logger.debug(`Message from ${session.username}: ${session.content}`)
      return next()
    })
  }
}

// 注册命令
function registerCommands(ctx: Context, config: Config, logger: Logger) {
  // 主命令组
  const cmd = ctx.command('example', '示例插件命令')
  
  // Hello 命令
  cmd.subcommand('.hello [name]', '打招呼')
    .action(async ({ session }, name) => {
      const targetName = name || session.username
      return `${config.prefix}: Hello, ${targetName}! 👋`
    })
  
  // 积分系统命令
  const pointsCmd = cmd.subcommand('.points', '积分系统')
  
  // 查看积分
  pointsCmd.subcommand('.check', '查看积分')
    .action(async ({ session }) => {
      try {
        const user = await getOrCreateUser(ctx, session)
        return `${config.prefix}: 你的积分: ${user.points} 💰`
      } catch (error) {
        logger.error('Failed to check points:', error)
        return '查询积分失败，请稍后重试。'
      }
    })
  
  // 每日签到
  pointsCmd.subcommand('.signin', '每日签到')
    .action(async ({ session }) => {
      try {
        const user = await getOrCreateUser(ctx, session)
        const now = new Date()
        const lastSignIn = new Date(user.lastSignIn)
        
        // 检查是否已经签到
        if (isSameDay(now, lastSignIn)) {
          return '今天已经签到过了！明天再来吧 📅'
        }
        
        // 检查积分上限
        const newPoints = Math.min(
          user.points + config.pointsConfig.dailyBonus,
          config.pointsConfig.maxPoints
        )
        
        // 更新用户数据
        await ctx.database.set('example_users', { userId: session.userId }, {
          points: newPoints,
          lastSignIn: now,
          updatedAt: now,
        })
        
        // 记录日志
        await logAction(ctx, session.userId, 'signin', `Gained ${config.pointsConfig.dailyBonus} points`)
        
        return `${config.prefix}: 签到成功！获得 ${config.pointsConfig.dailyBonus} 积分 ✨\n当前积分: ${newPoints}`
      } catch (error) {
        logger.error('Failed to sign in:', error)
        return '签到失败，请稍后重试。'
      }
    })
  
  // 随机数生成器
  cmd.subcommand('.random', '生成随机数')
    .option('min', '-m <number> 最小值', { fallback: 1 })
    .option('max', '-M <number> 最大值', { fallback: 100 })
    .action(({ options }) => {
      const { min, max } = options
      if (min >= max) {
        return '最小值必须小于最大值！'
      }
      const result = Math.floor(Math.random() * (max - min + 1)) + min
      return `${config.prefix}: 随机数: ${result} 🎲`
    })
  
  // 管理员命令
  const adminCmd = cmd.subcommand('.admin', '管理员命令')
    .check(({ session }) => {
      if (!config.adminUsers.includes(session.userId)) {
        return '权限不足：需要管理员权限。'
      }
    })
  
  // 重置积分 (管理员)
  if (config.pointsConfig.enableReset) {
    adminCmd.subcommand('.reset <userId>', '重置用户积分')
      .action(async ({ session }, userId) => {
        try {
          await ctx.database.set('example_users', { userId }, {
            points: 0,
            updatedAt: new Date(),
          })
          
          await logAction(ctx, session.userId, 'admin_reset', `Reset points for user ${userId}`)
          
          return `${config.prefix}: 已重置用户 ${userId} 的积分。`
        } catch (error) {
          logger.error('Failed to reset points:', error)
          return '重置积分失败。'
        }
      })
  }
  
  // 查看日志 (管理员)
  adminCmd.subcommand('.logs [limit:number]', '查看操作日志')
    .action(async (_, limit = 10) => {
      try {
        const logs = await ctx.database
          .select('example_logs')
          .orderBy('timestamp', 'desc')
          .limit(limit)
          .execute()
        
        if (logs.length === 0) {
          return '暂无日志记录。'
        }
        
        const logText = logs.map(log => 
          `[${log.timestamp.toLocaleString()}] ${log.userId}: ${log.action} - ${log.details}`
        ).join('\n')
        
        return `${config.prefix}: 最近 ${logs.length} 条日志:\n${logText}`
      } catch (error) {
        logger.error('Failed to fetch logs:', error)
        return '获取日志失败。'
      }
    })
}

// 注册事件监听器
function registerEventListeners(ctx: Context, config: Config, logger: Logger) {
  // 用户加入群组
  ctx.on('guild-member-added', async (session) => {
    if (config.enableLogging) {
      logger.info(`User ${session.username} joined guild ${session.guildId}`)
    }
    
    // 创建用户记录
    await getOrCreateUser(ctx, session)
    
    // 发送欢迎消息
    session.send(`${config.prefix}: 欢迎 ${session.username} 加入群组！🎉\n输入 "example.hello" 开始使用插件功能。`)
  })
  
  // 监听特定消息
  ctx.on('message', async (session) => {
    if (session.content === '积分') {
      const user = await getOrCreateUser(ctx, session)
      session.send(`你的积分: ${user.points} 💰`)
    }
  })
}

// 辅助函数：获取或创建用户
async function getOrCreateUser(ctx: Context, session: Session): Promise<ExampleUser> {
  let [user] = await ctx.database.get('example_users', { userId: session.userId })
  
  if (!user) {
    const now = new Date()
    user = await ctx.database.create('example_users', {
      userId: session.userId,
      username: session.username,
      points: 0,
      lastSignIn: new Date(0), // 初始化为很久以前的时间
      createdAt: now,
      updatedAt: now,
    })
  }
  
  return user
}

// 辅助函数：记录操作日志
async function logAction(ctx: Context, userId: string, action: string, details: string) {
  await ctx.database.create('example_logs', {
    userId,
    action,
    details,
    timestamp: new Date(),
  })
}

// 辅助函数：检查是否为同一天
function isSameDay(date1: Date, date2: Date): boolean {
  return date1.toDateString() === date2.toDateString()
}
