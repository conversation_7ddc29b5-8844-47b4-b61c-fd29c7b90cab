#!/bin/bash

# 容器管理功能演示脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

# 创建测试容器
create_test_container() {
    log "INFO" "创建测试容器用于演示..."
    
    # 创建一个简单的测试容器
    docker run -d \
        --name "koishi-bot-5140" \
        --restart unless-stopped \
        -p "5140:80" \
        nginx:alpine >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log "SUCCESS" "测试容器创建成功"
        log "INFO" "容器名称: koishi-bot-5140"
        log "INFO" "端口映射: 5140:80"
    else
        log "ERROR" "测试容器创建失败"
        return 1
    fi
}

# 显示容器状态
show_demo_status() {
    echo
    echo -e "${GREEN}========================================"
    echo "    容器管理功能演示"
    echo "========================================"
    echo -e "${NC}"
    
    echo -e "${BLUE}当前 Docker 容器状态：${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}" | grep -E "koishi|nginx" || echo "没有相关容器"
    
    echo
    echo -e "${BLUE}端口占用情况：${NC}"
    netstat -tuln 2>/dev/null | grep ":5140 " || echo "端口 5140 未被占用"
    
    echo
}

# 演示交互式选择
demo_interactive_menu() {
    echo -e "${YELLOW}模拟检测到现有容器的情况...${NC}"
    echo
    echo -e "${YELLOW}请选择操作：${NC}"
    echo "1) 查看现有容器状态和日志"
    echo "2) 重启现有容器"
    echo "3) 停止并重新部署新容器"
    echo "4) 取消操作并退出脚本"
    echo
    read -p "请输入选项 (1-4): " choice
    
    case $choice in
        1)
            echo
            log "INFO" "显示容器状态..."
            show_demo_status
            
            echo -e "${YELLOW}容器日志 (最后5行)：${NC}"
            docker logs --tail 5 "koishi-bot-5140" 2>/dev/null || echo "无法获取日志"
            echo
            
            # 递归调用菜单
            demo_interactive_menu
            ;;
        2)
            echo
            log "INFO" "重启容器..."
            docker restart "koishi-bot-5140" >/dev/null 2>&1
            if [ $? -eq 0 ]; then
                log "SUCCESS" "容器重启成功！"
                echo
                echo -e "${GREEN}🎉 重启完成！${NC}"
                echo -e "${BLUE}访问地址: http://localhost:5140${NC}"
            else
                log "ERROR" "容器重启失败"
            fi
            ;;
        3)
            echo
            log "INFO" "停止并删除现有容器..."
            docker stop "koishi-bot-5140" >/dev/null 2>&1
            docker rm "koishi-bot-5140" >/dev/null 2>&1
            log "SUCCESS" "容器已删除，可以继续部署新容器"
            ;;
        4)
            log "INFO" "用户取消操作"
            ;;
        *)
            log "ERROR" "无效选项，请重新选择"
            demo_interactive_menu
            ;;
    esac
}

# 清理测试环境
cleanup_demo() {
    log "INFO" "清理演示环境..."
    docker stop "koishi-bot-5140" >/dev/null 2>&1
    docker rm "koishi-bot-5140" >/dev/null 2>&1
    log "SUCCESS" "清理完成"
}

# 主函数
main() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi 容器管理功能演示"
    echo "========================================"
    echo -e "${NC}"
    
    echo "这个演示将展示新的容器检测和管理功能"
    echo "包括："
    echo "• 检测现有容器"
    echo "• 交互式选择操作"
    echo "• 重启现有容器"
    echo "• 查看容器状态和日志"
    echo
    
    read -p "按 Enter 开始演示，或 Ctrl+C 退出: "
    
    # 检查 Docker
    if ! command -v docker >/dev/null 2>&1; then
        log "ERROR" "Docker 未安装，无法演示"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log "ERROR" "Docker 服务未运行"
        exit 1
    fi
    
    # 清理可能存在的测试容器
    cleanup_demo
    
    # 显示初始状态
    show_demo_status
    
    # 创建测试容器
    if ! create_test_container; then
        log "ERROR" "无法创建测试容器"
        exit 1
    fi
    
    echo
    log "SUCCESS" "测试环境准备完成"
    echo
    
    # 显示更新后的状态
    show_demo_status
    
    # 演示交互式菜单
    demo_interactive_menu
    
    echo
    echo -e "${GREEN}演示完成！${NC}"
    echo
    echo "在实际的 Koishi 部署脚本中："
    echo "• koishi-docker-deploy.sh - 完整部署脚本"
    echo "• koishi-quick-deploy.sh - 快速部署脚本"
    echo "都已集成了这些容器管理功能"
    echo
    
    read -p "是否清理测试容器? (y/N): " cleanup_choice
    if [[ $cleanup_choice == [yY] ]]; then
        cleanup_demo
    else
        log "INFO" "测试容器保留，您可以手动清理: docker rm -f koishi-bot-5140"
    fi
}

# 信号处理
trap cleanup_demo EXIT

main "$@"
