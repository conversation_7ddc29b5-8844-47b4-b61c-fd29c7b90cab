# English language pack
commands:
  example:
    description: Example plugin commands
    
    hello:
      description: Say hello
      usage: example.hello [name]
      messages:
        greeting: "{0}: Hello, {1}! 👋"
    
    points:
      description: Points system
      
      check:
        description: Check points
        messages:
          current: "{0}: Your points: {1} 💰"
          error: "Failed to check points, please try again later."
      
      signin:
        description: Daily sign-in
        messages:
          success: "{0}: Sign-in successful! Gained {1} points ✨\nCurrent points: {2}"
          already: "You have already signed in today! Come back tomorrow 📅"
          error: "Sign-in failed, please try again later."
    
    random:
      description: Generate random number
      usage: example.random [-m minimum] [-M maximum]
      messages:
        result: "{0}: Random number: {1} 🎲"
        invalid: "Minimum value must be less than maximum value!"
    
    admin:
      description: Admin commands
      
      reset:
        description: Reset user points
        usage: example.admin.reset <userId>
        messages:
          success: "{0}: Reset points for user {1}."
          error: "Failed to reset points."
      
      logs:
        description: View operation logs
        usage: example.admin.logs [limit]
        messages:
          header: "{0}: Recent {1} logs:"
          empty: "No logs found."
          error: "Failed to fetch logs."

errors:
  permission_denied: "Permission denied: Admin privileges required."
  message_too_long: "Message too long! Maximum length is {0} characters."
  database_error: "Database operation failed, please try again later."
  invalid_input: "Invalid input, please check parameter format."

events:
  welcome: "{0}: Welcome {1} to the group! 🎉\nType \"example.hello\" to start using plugin features."
  
messages:
  points_query: "Your points: {0} 💰"
  
config:
  prefix:
    description: "Prefix for reply messages"
  maxMessageLength:
    description: "Maximum message length limit"
  enableLogging:
    description: "Enable detailed logging"
  adminUsers:
    description: "List of admin user IDs"
  pointsConfig:
    description: "Points system configuration"
    dailyBonus:
      description: "Daily sign-in bonus points"
    maxPoints:
      description: "Maximum points limit"
    enableReset:
      description: "Allow points reset"
