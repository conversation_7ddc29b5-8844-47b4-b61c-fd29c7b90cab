#!/bin/bash

#==============================================================================
# Koishi 访问问题一键修复脚本
# 
# 自动诊断并修复 Koishi 无法访问的问题
# 
# 使用方法：
#   ./koishi-fix-access.sh
#
# 版本：v1.0.0
#==============================================================================

# 注释掉 set -e 以避免脚本因为单个命令失败而退出
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目路径 - 支持多个可能的位置
POSSIBLE_PATHS=(
    "$HOME/koishi-bot"
    "$HOME/koishi"
    "./koishi-bot"
    "./koishi"
    "$(pwd)/koishi-bot"
    "$(pwd)/koishi"
)

# 查找现有的 Koishi 项目
find_koishi_project() {
    for path in "${POSSIBLE_PATHS[@]}"; do
        if [ -d "$path" ] && [ -f "$path/package.json" ]; then
            # 检查是否是 Koishi 项目
            if grep -q "koishi" "$path/package.json" 2>/dev/null; then
                echo "$path"
                return 0
            fi
        fi
    done
    return 1
}

# 设置项目路径
if PROJECT_PATH=$(find_koishi_project); then
    log "INFO" "找到 Koishi 项目: $PROJECT_PATH"
else
    PROJECT_PATH="$HOME/koishi-bot"
    log "WARN" "未找到现有的 Koishi 项目"
fi

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    
    case "$level" in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "FIX")
            echo -e "${CYAN}[FIX]${NC} $message"
            ;;
    esac
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 显示标题
show_header() {
    clear
    echo -e "${CYAN}"
    echo "========================================"
    echo "    Koishi 访问问题一键修复脚本"
    echo "========================================"
    echo -e "${NC}"
}

# 步骤1：停止现有进程
stop_existing_processes() {
    log "INFO" "步骤1：停止现有的 Koishi 进程..."
    
    # 查找并停止 Koishi 相关进程
    local pids=$(ps aux | grep -E "(koishi|node.*koishi)" | grep -v grep | awk '{print $2}' || true)

    if [ -n "$pids" ]; then
        log "FIX" "发现运行中的进程，正在停止..."
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2

        # 强制停止仍在运行的进程
        local remaining_pids=$(ps aux | grep -E "(koishi|node.*koishi)" | grep -v grep | awk '{print $2}' || true)
        if [ -n "$remaining_pids" ]; then
            echo "$remaining_pids" | xargs kill -KILL 2>/dev/null || true
        fi

        log "SUCCESS" "进程已停止"
    else
        log "INFO" "没有发现运行中的 Koishi 进程"
    fi

    return 0
}

# 步骤2：安装必要工具
install_tools() {
    log "INFO" "步骤2：安装必要工具..."

    local tools_needed=()
    local build_tools_needed=()

    # 检查网络诊断工具
    if ! command_exists netstat; then
        tools_needed+=("net-tools")
    fi

    if ! command_exists lsof; then
        tools_needed+=("lsof")
    fi

    # 检查编译工具（Node.js 原生模块需要）
    if ! command_exists gcc; then
        build_tools_needed+=("build-essential")
    fi

    if ! command_exists python3; then
        build_tools_needed+=("python3")
    fi

    if ! command_exists make; then
        build_tools_needed+=("make")
    fi

    if ! command_exists g++; then
        build_tools_needed+=("g++")
    fi

    # 合并所有需要的工具
    local all_tools=("${tools_needed[@]}" "${build_tools_needed[@]}")

    if [ ${#all_tools[@]} -gt 0 ]; then
        log "FIX" "安装工具: ${all_tools[*]}"
        sudo apt update >/dev/null 2>&1
        sudo apt install -y "${all_tools[@]}" >/dev/null 2>&1
        log "SUCCESS" "工具安装完成"

        # 如果安装了编译工具，建议重新安装 npm 包
        if [ ${#build_tools_needed[@]} -gt 0 ]; then
            log "INFO" "检测到安装了编译工具，建议重新安装 npm 包以重新编译原生模块"
        fi
    else
        log "SUCCESS" "所需工具已安装"
    fi
}

# 步骤2.5：重新安装 npm 包（如果安装了编译工具）
reinstall_npm_packages() {
    log "INFO" "步骤2.5：检查是否需要重新安装 npm 包..."

    # 检查是否存在 package.json
    if [ ! -f "$PROJECT_PATH/package.json" ]; then
        log "WARN" "未找到 package.json，跳过 npm 包重新安装"
        return 0
    fi

    # 检查是否存在 node_modules
    if [ ! -d "$PROJECT_PATH/node_modules" ]; then
        log "INFO" "未找到 node_modules，将进行全新安装"
        cd "$PROJECT_PATH" || return 1
        log "FIX" "安装 npm 包..."
        npm install >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            log "SUCCESS" "npm 包安装完成"
        else
            log "WARN" "npm 包安装可能有问题，但继续执行..."
        fi
        return 0
    fi

    # 检查是否有原生模块需要重新编译
    local native_modules_exist=false
    if [ -d "$PROJECT_PATH/node_modules" ]; then
        # 检查常见的需要编译的模块
        for module in "node-gyp" "sqlite3" "canvas" "sharp" "bcrypt" "argon2"; do
            if [ -d "$PROJECT_PATH/node_modules/$module" ]; then
                native_modules_exist=true
                break
            fi
        done
    fi

    if [ "$native_modules_exist" = true ]; then
        log "FIX" "检测到原生模块，重新编译..."
        cd "$PROJECT_PATH" || return 1
        npm rebuild >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            log "SUCCESS" "原生模块重新编译完成"
        else
            log "WARN" "原生模块重新编译可能有问题，尝试重新安装..."
            rm -rf node_modules package-lock.json >/dev/null 2>&1
            npm install >/dev/null 2>&1
            if [ $? -eq 0 ]; then
                log "SUCCESS" "npm 包重新安装完成"
            else
                log "WARN" "npm 包重新安装有问题，但继续执行..."
            fi
        fi
    else
        log "SUCCESS" "未检测到需要重新编译的原生模块"
    fi
}

# 步骤3：修复配置文件
fix_config_file() {
    log "INFO" "步骤3：修复 Koishi 配置文件..."
    
    # 确保项目目录存在
    if [ ! -d "$PROJECT_PATH" ]; then
        log "WARN" "项目目录不存在: $PROJECT_PATH"
        log "INFO" "正在创建新的 Koishi 项目..."

        # 创建项目目录
        mkdir -p "$PROJECT_PATH"
        cd "$PROJECT_PATH"

        # 初始化新的 Koishi 项目
        log "INFO" "初始化 package.json..."
        cat > package.json << 'EOF'
{
  "name": "koishi-bot",
  "version": "1.0.0",
  "description": "A Koishi bot project",
  "main": "index.js",
  "scripts": {
    "start": "koishi start",
    "dev": "koishi start --watch"
  },
  "dependencies": {
    "koishi": "^4.15.0",
    "@koishijs/plugin-console": "^5.25.0",
    "@koishijs/plugin-dataview": "^2.5.0",
    "@koishijs/plugin-status": "^2.11.0"
  }
}
EOF

        # 创建基本的配置文件
        log "INFO" "创建基本配置文件..."
        cat > koishi.yml << 'EOF'
host: 0.0.0.0
port: 5140
plugins:
  console:
    open: false
  status: {}
  dataview: {}
EOF

        log "SUCCESS" "项目初始化完成"
    fi
    
    # 备份原配置文件
    if [ -f "$PROJECT_PATH/koishi.yml" ]; then
        cp "$PROJECT_PATH/koishi.yml" "$PROJECT_PATH/koishi.yml.backup.$(date +%Y%m%d_%H%M%S)"
        log "FIX" "已备份原配置文件"
    fi
    
    # 创建正确的配置文件
    cat > "$PROJECT_PATH/koishi.yml" << 'EOF'
host: 0.0.0.0
port: 5140

database:
  type: sqlite
  path: ./data/koishi.db

plugins:
  console:
    open: false
  dataview: {}

logger:
  levels:
    base: 2
EOF
    
    log "SUCCESS" "配置文件已修复"
    
    # 确保数据目录存在
    mkdir -p "$PROJECT_PATH/data"
    mkdir -p "$PROJECT_PATH/logs"
    
    log "SUCCESS" "项目目录结构已完善"
}

# 步骤4：配置防火墙
configure_firewall() {
    log "INFO" "步骤4：配置防火墙规则..."
    
    # 检查 UFW 状态
    local ufw_status=$(sudo ufw status 2>/dev/null | head -1 | awk '{print $2}')
    
    if [ "$ufw_status" = "inactive" ]; then
        log "INFO" "UFW 防火墙未启用"
        # 添加规则但不启用防火墙
        sudo ufw allow 5140/tcp >/dev/null 2>&1
        log "FIX" "已添加 5140 端口规则（防火墙未启用）"
    else
        log "INFO" "UFW 防火墙已启用"
        sudo ufw allow 5140/tcp >/dev/null 2>&1
        log "FIX" "已开放 5140 端口"
    fi
    
    # 检查其他防火墙
    if command_exists firewall-cmd; then
        log "FIX" "配置 firewalld..."
        sudo firewall-cmd --permanent --add-port=5140/tcp >/dev/null 2>&1 || true
        sudo firewall-cmd --reload >/dev/null 2>&1 || true
    fi
    
    log "SUCCESS" "防火墙配置完成"
}

# 步骤5：启动 Koishi
start_koishi() {
    log "INFO" "步骤5：启动 Koishi 服务..."
    
    cd "$PROJECT_PATH"
    
    # 检查 package.json 和 node_modules
    if [ ! -f "package.json" ]; then
        log "ERROR" "package.json 不存在，无法启动 Koishi"
        log "INFO" "请确保在正确的 Koishi 项目目录中运行此脚本"
        return 1
    fi

    if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
        log "FIX" "安装依赖包..."
        log "INFO" "这可能需要几分钟时间，请耐心等待..."

        # 清理可能存在的缓存
        npm cache clean --force >/dev/null 2>&1

        # 安装依赖
        if npm install --no-audit --no-fund; then
            log "SUCCESS" "依赖包安装完成"
        else
            log "WARN" "依赖包安装可能有问题，但继续执行..."
        fi
    else
        log "INFO" "依赖包已存在，跳过安装"
    fi
    
    # 创建启动脚本
    cat > start.sh << EOF
#!/bin/bash
# 确保切换到正确的项目目录
cd "$PROJECT_PATH" || {
    echo "错误: 无法切换到项目目录 $PROJECT_PATH"
    exit 1
}

echo "启动 Koishi..."
echo "项目目录: \$(pwd)"
echo "访问地址: http://\$(curl -s ifconfig.me):5140"
echo "本地地址: http://localhost:5140"
echo "按 Ctrl+C 停止服务"
echo "=========================="

# 检查 package.json 是否存在
if [ ! -f "package.json" ]; then
    echo "错误: package.json 不存在"
    echo "当前目录: \$(pwd)"
    echo "请确保在正确的 Koishi 项目目录中运行此脚本"
    exit 1
fi

npm start
EOF
    chmod +x start.sh
    
    log "SUCCESS" "启动脚本已创建"
    log "INFO" "正在后台启动 Koishi..."

    # 确保在项目目录中
    cd "$PROJECT_PATH"

    # 创建日志目录
    mkdir -p logs

    # 后台启动 Koishi
    nohup npm start > logs/koishi.log 2>&1 &
    local koishi_pid=$!
    
    # 等待启动
    log "INFO" "等待服务启动..."
    sleep 5
    
    # 检查进程是否还在运行
    if kill -0 $koishi_pid 2>/dev/null; then
        log "SUCCESS" "Koishi 已在后台启动 (PID: $koishi_pid)"
        echo $koishi_pid > "$PROJECT_PATH/koishi.pid"
    else
        log "ERROR" "Koishi 启动失败"
        log "INFO" "查看错误日志: cat $PROJECT_PATH/logs/koishi.log"
        log "INFO" "您可以手动运行以下命令来启动："
        log "INFO" "cd $PROJECT_PATH && npm start"
        return 1
    fi
}

# 步骤6：验证访问
verify_access() {
    log "INFO" "步骤6：验证服务访问..."
    
    # 等待服务完全启动
    sleep 3
    
    # 检查端口监听
    local port_check=$(sudo netstat -tlnp 2>/dev/null | grep ":5140" || sudo ss -tlnp 2>/dev/null | grep ":5140")
    
    if [ -n "$port_check" ]; then
        log "SUCCESS" "端口 5140 正在监听"
        echo "  $port_check"
    else
        log "WARNING" "端口 5140 未检测到监听状态"
    fi
    
    # 测试本地访问
    if curl -s -I http://localhost:5140 >/dev/null 2>&1; then
        log "SUCCESS" "本地访问测试通过"
    else
        log "WARNING" "本地访问测试失败"
    fi
    
    # 获取外部IP
    local external_ip=$(curl -s ifconfig.me 2>/dev/null || curl -s ipinfo.io/ip 2>/dev/null || echo "未知")
    
    log "SUCCESS" "服务验证完成"
    
    # 显示访问信息
    echo
    echo -e "${GREEN}🎉 Koishi 修复完成！${NC}"
    echo
    echo -e "${BLUE}访问地址：${NC}"
    echo "  外部访问: http://$external_ip:5140"
    echo "  本地访问: http://localhost:5140"
    echo "  内网访问: http://127.0.0.1:5140"
    echo
    echo -e "${BLUE}管理命令：${NC}"
    echo "  查看日志: tail -f $PROJECT_PATH/logs/koishi.log"
    echo "  停止服务: kill \$(cat $PROJECT_PATH/koishi.pid)"
    echo "  重启服务: cd $PROJECT_PATH && ./start.sh"
    echo
    echo -e "${YELLOW}注意事项：${NC}"
    echo "• 如果使用云服务器，请确保安全组开放 5140 端口"
    echo "• 首次访问可能需要几分钟初始化"
    echo "• 如果仍无法访问，请检查云服务器防火墙设置"
    echo
}

# 创建停止脚本
create_stop_script() {
    cat > "$PROJECT_PATH/stop.sh" << EOF
#!/bin/bash
# 确保切换到正确的项目目录
cd "$PROJECT_PATH" || {
    echo "错误: 无法切换到项目目录 $PROJECT_PATH"
    exit 1
}

echo "正在停止 Koishi 服务..."

if [ -f koishi.pid ]; then
    PID=\$(cat koishi.pid)
    if kill -0 \$PID 2>/dev/null; then
        echo "停止 Koishi 服务 (PID: \$PID)..."
        kill \$PID
        rm -f koishi.pid
        echo "服务已停止"
    else
        echo "进程不存在，清理 PID 文件"
        rm -f koishi.pid
    fi
else
    echo "未找到 PID 文件，尝试查找进程..."
    pkill -f "npm.*start" || pkill -f koishi || echo "未找到运行中的进程"
fi
EOF
    chmod +x "$PROJECT_PATH/stop.sh"
}

# 主函数
main() {
    show_header

    log "INFO" "开始一键修复 Koishi 访问问题..."
    echo

    # 执行各个步骤，如果某个步骤失败则继续执行其他步骤
    if ! stop_existing_processes; then
        log "WARN" "停止进程步骤有问题，但继续执行..."
    fi
    echo

    if ! install_tools; then
        log "WARN" "工具安装步骤有问题，但继续执行..."
    fi
    echo

    if ! reinstall_npm_packages; then
        log "WARN" "npm 包重新安装步骤有问题，但继续执行..."
    fi
    echo

    if ! fix_config_file; then
        log "WARN" "配置文件修复步骤有问题，但继续执行..."
    fi
    echo

    configure_firewall
    echo

    if ! start_koishi; then
        log "ERROR" "Koishi 启动失败，请检查错误信息"
        log "INFO" "您可以手动运行以下命令来启动："
        log "INFO" "cd $PROJECT_PATH && npm start"
        return 1
    fi
    echo

    create_stop_script

    verify_access

    log "SUCCESS" "修复完成！"
}

# 执行主函数
main "$@"
