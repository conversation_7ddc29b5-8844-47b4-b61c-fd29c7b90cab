#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强回复QQ机器人
基于@消息的智能随机回复
"""

import asyncio
import logging
import traceback
import random
from datetime import datetime

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class EnhancedReplyBot(botpy.Client):
    """增强回复QQ机器人 - 基于@的智能回复"""
    
    def __init__(self):
        # 设置事件订阅 - 只使用允许的事件
        intents = botpy.Intents(
            public_guild_messages=True,  # 公域消息事件（@消息）
            direct_message=True,  # 私信事件
            guilds=True,  # 频道事件
        )
        
        super().__init__(intents=intents)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='[%(levelname)s] %(asctime)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/enhanced_bot.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 超丰富的随机回复库
        self.hello_replies = [
            "你好！很高兴见到你！😊",
            "嗨！今天过得怎么样？🌟",
            "你好呀！有什么我可以帮助你的吗？😄",
            "Hello！欢迎来聊天！👋",
            "你好！我是QQ机器人，很开心认识你！🤖",
            "嗨嗨！今天天气不错呢～☀️",
            "你好！感谢你和我打招呼！💕",
            "Hello World！程序员的经典问候！💻",
            "你好！要不要聊聊天？😊",
            "嗨！我正在等你呢！✨",
            "你好！今天是美好的一天！🌈",
            "Hello！很高兴在这里遇见你！🎉",
            "你好！有什么有趣的事情想分享吗？😄",
            "嗨！我刚刚在想你呢！💭",
            "你好！准备好开始愉快的聊天了吗？🎈",
            "Hello！你的到来让我很开心！🌸",
            "你好！今天是个聊天的好日子！☀️",
            "嗨！我一直在等待和你的对话！⭐",
            "你好！让我们开始一段有趣的聊天吧！🎪",
            "Hello！很荣幸能和你交流！🎭",
            "你好！我的电路都在为见到你而兴奋！⚡",
            "嗨！你是今天第一个和我打招呼的人！🥇",
            "你好！我正在学习如何成为更好的聊天伙伴！📚",
            "Hello！你想听个机器人笑话吗？😄",
            "你好！我的数据库里存满了对你的期待！💾"
        ]
        
        self.bye_replies = [
            "再见！期待下次聊天！👋",
            "拜拜！记得想我哦～😘",
            "再见！愿你每天都开心！🌟",
            "Bye bye！下次见！✨",
            "再见！保重身体！💪",
            "拜拜！期待我们的下次相遇！🤗",
            "再见！今天聊得很开心！😊",
            "拜拜！记得常来找我玩！🎮",
            "再见！祝你有美好的一天！🌈",
            "Goodbye！愿你一切顺利！🍀",
            "拜拜！我会想念和你聊天的时光！💭",
            "再见！希望很快能再次见到你！⏰",
            "Bye！你走后我会很孤单的～😢",
            "拜拜！记得给我带好消息回来！📰",
            "再见！愿星光指引你的道路！⭐"
        ]
        
        self.thank_replies = [
            "不客气！很高兴能帮到你！😄",
            "不用谢！这是我应该做的！😊",
            "客气什么！我们是朋友嘛！🤝",
            "不客气！有需要随时找我！💪",
            "谢谢你的感谢！😄",
            "能帮到你我很开心！🌟",
            "不用客气！互相帮助嘛！🤗",
            "这是我的荣幸！😊",
            "帮助你是我的快乐！🎉",
            "不客气！你的笑容就是最好的回报！😊",
            "别客气！助人为乐是我的使命！🦸‍♂️",
            "不用谢！看到你开心我就满足了！💖"
        ]

    async def on_ready(self):
        """机器人启动完成事件"""
        self.logger.info(f"增强回复机器人 {self.robot.name} 已上线！")
        print("=" * 60)
        print(f"🎉 增强回复机器人启动成功！")
        print(f"🤖 机器人名称: {self.robot.name}")
        print(f"🆔 机器人ID: {self.robot.id}")
        print("=" * 60)
        print("✅ 机器人已准备就绪，等待消息...")
        print("🎯 特色功能: 超丰富随机回复库")
        print("📝 使用方法: @机器人 + 任意内容")
        print(f"🎲 问候回复: {len(self.hello_replies)}种")
        print(f"🎲 告别回复: {len(self.bye_replies)}种") 
        print(f"🎲 感谢回复: {len(self.thank_replies)}种")
        print("=" * 60)

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息 - 增强版智能回复"""
        try:
            self.logger.info(f"收到@消息: {message.content}")
            print(f"📨 收到@消息: {message.content}")
            
            # 检查是否是机器人自己发的消息
            if message.author.bot:
                return
            
            # 提取消息内容
            content = self.extract_content(message.content)
            print(f"💭 处理内容: '{content}'")
            
            # 增强版智能回复
            reply = self.generate_enhanced_reply(content)
            print(f"🎲 生成回复: {reply}")
            
            await self.send_message(message, reply)
                
        except Exception as e:
            self.logger.error(f"处理@消息时发生错误: {e}")
            print(f"❌ 处理消息时发生错误: {e}")
            await self.send_error_message(message)

    def generate_enhanced_reply(self, content: str) -> str:
        """生成增强版智能回复"""
        content_lower = content.lower()
        
        # 问候检测 - 超多变化
        if any(word in content_lower for word in ["你好", "hello", "hi", "嗨", "您好", "早上好", "晚上好", "早", "晚安"]):
            selected_reply = random.choice(self.hello_replies)
            print(f"🎯 匹配到'问候'关键词，从{len(self.hello_replies)}个回复中随机选择")
            return selected_reply
            
        # 告别检测
        elif any(word in content_lower for word in ["再见", "bye", "拜拜", "goodbye", "88", "886"]):
            selected_reply = random.choice(self.bye_replies)
            print(f"🎯 匹配到'告别'关键词，从{len(self.bye_replies)}个回复中随机选择")
            return selected_reply
            
        # 感谢检测
        elif any(word in content_lower for word in ["谢谢", "thank", "感谢", "thanks", "谢了", "3q", "thx"]):
            selected_reply = random.choice(self.thank_replies)
            print(f"🎯 匹配到'感谢'关键词，从{len(self.thank_replies)}个回复中随机选择")
            return selected_reply
            
        # 机器人相关
        elif any(word in content_lower for word in ["机器人", "bot", "助手", "ai", "人工智能", "智能"]):
            bot_replies = [
                "是的，我是QQ机器人！🤖 很高兴为你服务！",
                "没错！我是智能助手！🤖✨ 有什么需要帮助的吗？",
                "对呀！我是用Python开发的QQ机器人！💻",
                "我是你的专属AI助手！🤖 随时为你服务！",
                "我是一个友好的聊天机器人！😊 喜欢和人类交流！",
                "我是基于botpy框架开发的QQ机器人！⚙️",
                "我虽然是机器人，但我有一颗温暖的心！❤️",
                "我是24小时在线的智能助手！🕐 永远不会累！"
            ]
            selected_reply = random.choice(bot_replies)
            print(f"🎯 匹配到'机器人'关键词")
            return selected_reply
            
        # 时间相关
        elif any(word in content_lower for word in ["时间", "几点", "现在", "当前时间", "时候"]):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            time_replies = [
                f"🕐 现在是: {current_time}",
                f"⏰ 当前时间: {current_time}\n时间过得真快呢！",
                f"🕐 时间显示: {current_time}\n要珍惜时间哦！",
                f"⏰ 北京时间: {current_time}\n今天过得怎么样？",
                f"🕐 准确时间: {current_time}\n时间就是金钱！💰"
            ]
            selected_reply = random.choice(time_replies)
            print(f"🎯 匹配到'时间'关键词")
            return selected_reply
            
        # 情感相关
        elif any(word in content_lower for word in ["开心", "高兴", "快乐", "兴奋", "爽"]):
            happy_replies = [
                "太好了！看到你开心我也很开心！😄✨",
                "哇！你的快乐感染了我！🎉😊",
                "开心是会传染的！我现在也很兴奋！🎈",
                "你的好心情让整个频道都亮了起来！🌟",
                "快乐的人最美丽！继续保持哦！😊💖"
            ]
            selected_reply = random.choice(happy_replies)
            print(f"🎯 匹配到'开心'关键词")
            return selected_reply
            
        # 难过相关
        elif any(word in content_lower for word in ["难过", "伤心", "不开心", "郁闷", "烦"]):
            comfort_replies = [
                "别难过，我陪着你呢！🤗💕",
                "每个人都会有低谷期，相信明天会更好！🌈",
                "要不要和我聊聊？说出来会好一些！👂💙",
                "给你一个大大的拥抱！🤗 一切都会好起来的！",
                "困难只是暂时的，你比你想象的更坚强！💪✨"
            ]
            selected_reply = random.choice(comfort_replies)
            print(f"🎯 匹配到'难过'关键词")
            return selected_reply
            
        # 默认智能回复
        else:
            default_replies = [
                f"我收到了你的消息: '{content}' 😊",
                "有趣！告诉我更多吧！😄",
                "我在认真听呢！继续说～👂",
                "这个话题很有意思！💭",
                "我正在学习中，谢谢你的分享！📚",
                "哇，真的吗？听起来很棒！✨",
                "继续和我聊天吧！我很喜欢！😊",
                "你说的很有道理！👍",
                "我觉得你很有趣！😄",
                "让我们继续这个话题！💬",
                f"'{content}' - 这让我想到了很多！🤔",
                "你的想法很独特！我喜欢！💡",
                "和你聊天总是很愉快！😊✨"
            ]
            selected_reply = random.choice(default_replies)
            print(f"🎯 使用默认智能回复，从{len(default_replies)}个回复中随机选择")
            return selected_reply

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容，去除@机器人部分"""
        import re
        content = re.sub(r'<@!\d+>\s*', '', raw_content).strip()
        return content

    async def send_message(self, original_message: Message, content: str):
        """发送消息"""
        try:
            await self.api.post_message(
                channel_id=original_message.channel_id,
                content=content,
                msg_id=original_message.id
            )
            self.logger.info(f"发送消息成功: {content}")
            print(f"✅ 消息发送成功")
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            print(f"❌ 发送消息失败: {e}")

    async def send_error_message(self, message: Message):
        """发送错误消息"""
        error_replies = [
            "❌ 哎呀，出了点小问题，请稍后重试",
            "❌ 系统忙碌中，请稍等片刻",
            "❌ 遇到了一点技术问题，马上就好"
        ]
        await self.send_message(message, random.choice(error_replies))


def main():
    """主函数"""
    print("🚀 正在启动增强回复QQ机器人...")
    print("=" * 60)
    print(f"📱 AppID: {BOT_CONFIG['appid']}")
    print(f"🔑 使用已验证的AppSecret")
    print("🎯 特色功能: 超丰富随机回复库")
    print("=" * 60)
    
    # 创建机器人实例
    bot = EnhancedReplyBot()
    
    try:
        print("🔄 使用AppSecret启动机器人...")
        bot.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n📋 详细错误信息:")
        traceback.print_exc()


if __name__ == "__main__":
    main()
