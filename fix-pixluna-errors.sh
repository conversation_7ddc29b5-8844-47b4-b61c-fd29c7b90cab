#!/bin/bash

# Koishi pixluna 插件错误修复脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

show_header() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi pixluna 错误修复工具"
    echo "========================================"
    echo -e "${NC}"
}

# 检查 Koishi 配置
check_koishi_config() {
    log "INFO" "检查 Koishi 配置文件..."
    
    if [ ! -f "koishi.yml" ]; then
        log "ERROR" "未找到 koishi.yml 配置文件"
        log "INFO" "请确保在 Koishi 项目根目录运行此脚本"
        exit 1
    fi
    
    log "SUCCESS" "找到配置文件"
}

# 检查插件状态
check_plugin_status() {
    log "INFO" "检查 pixluna 插件状态..."
    
    # 检查是否安装了 pixluna 插件
    if npm list koishi-plugin-pixluna >/dev/null 2>&1; then
        local version=$(npm list koishi-plugin-pixluna --depth=0 2>/dev/null | grep koishi-plugin-pixluna | awk '{print $2}')
        log "SUCCESS" "pixluna 插件已安装，版本: $version"
    else
        log "WARN" "pixluna 插件未安装或版本有问题"
        return 1
    fi
    
    # 检查插件配置
    if grep -q "pixluna" koishi.yml; then
        log "SUCCESS" "在配置文件中找到 pixluna 插件配置"
    else
        log "WARN" "配置文件中未找到 pixluna 插件配置"
    fi
}

# 诊断网络连接
diagnose_network() {
    log "INFO" "诊断网络连接..."
    
    # 测试基本网络连接
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log "SUCCESS" "网络连接正常"
    else
        log "ERROR" "网络连接异常"
        return 1
    fi
    
    # 测试 HTTPS 连接
    if curl -s --connect-timeout 5 https://www.google.com >/dev/null 2>&1; then
        log "SUCCESS" "HTTPS 连接正常"
    else
        log "WARN" "HTTPS 连接可能有问题"
    fi
}

# 检查 QQ 适配器配置
check_qq_adapter() {
    log "INFO" "检查 QQ 适配器配置..."
    
    # 检查是否使用沙盒环境
    if grep -q "sandbox.api.sgroup.qq.com" koishi.yml; then
        log "WARN" "检测到使用 QQ 沙盒环境"
        log "INFO" "沙盒环境功能有限，建议切换到正式环境"
    fi
    
    # 检查 QQ 适配器版本
    if npm list @satorijs/adapter-qq >/dev/null 2>&1; then
        local version=$(npm list @satorijs/adapter-qq --depth=0 2>/dev/null | grep @satorijs/adapter-qq | awk '{print $2}')
        log "SUCCESS" "QQ 适配器已安装，版本: $version"
    else
        log "ERROR" "QQ 适配器未安装"
        return 1
    fi
}

# 提供修复建议
provide_solutions() {
    echo
    log "INFO" "提供修复建议..."
    echo
    
    echo -e "${YELLOW}针对 pixluna 'Not Found' 错误：${NC}"
    echo "1. 更新 pixluna 插件到最新版本"
    echo "2. 检查插件配置中的 API 端点"
    echo "3. 确认网络连接正常"
    echo "4. 查看插件文档了解最新配置要求"
    echo
    
    echo -e "${YELLOW}针对消息撤回错误 (code: 11255)：${NC}"
    echo "1. 检查机器人权限设置"
    echo "2. 确认消息未超过撤回时间限制"
    echo "3. 验证 QQ 机器人配置正确"
    echo "4. 考虑禁用自动撤回功能"
    echo
    
    echo -e "${YELLOW}通用解决方案：${NC}"
    echo "1. 重启 Koishi 服务"
    echo "2. 清理缓存和临时文件"
    echo "3. 检查日志获取更多信息"
    echo "4. 联系插件作者获取支持"
}

# 执行自动修复
auto_fix() {
    echo
    read -p "是否执行自动修复? (y/N): " AUTO_FIX
    
    if [[ $AUTO_FIX == [yY] ]]; then
        log "FIX" "开始自动修复..."
        
        # 1. 更新插件
        log "FIX" "更新 pixluna 插件..."
        npm update koishi-plugin-pixluna >/dev/null 2>&1
        
        # 2. 更新 QQ 适配器
        log "FIX" "更新 QQ 适配器..."
        npm update @satorijs/adapter-qq >/dev/null 2>&1
        
        # 3. 清理缓存
        log "FIX" "清理 npm 缓存..."
        npm cache clean --force >/dev/null 2>&1
        
        # 4. 重新安装依赖
        log "FIX" "重新安装依赖..."
        npm install >/dev/null 2>&1
        
        log "SUCCESS" "自动修复完成"
        
        echo
        echo -e "${GREEN}建议的配置调整：${NC}"
        echo
        
        # 生成建议的配置
        cat << 'EOF'
# 在 koishi.yml 中添加或修改以下配置：

plugins:
  pixluna:
    # 禁用自动撤回以避免错误
    autoRecall: false
    # 设置更长的超时时间
    timeout: 30000
    # 启用错误重试
    retryCount: 3
    
  # QQ 适配器配置优化
  adapter-qq:
    # 使用正式环境而非沙盒
    sandbox: false
    # 增加请求超时时间
    timeout: 10000
    # 启用错误重试
    retries: 2
EOF
        
    else
        log "INFO" "跳过自动修复"
    fi
}

# 生成配置模板
generate_config_template() {
    echo
    read -p "是否生成优化的配置模板? (y/N): " GEN_CONFIG
    
    if [[ $GEN_CONFIG == [yY] ]]; then
        log "INFO" "生成配置模板..."
        
        cat > "koishi-optimized.yml" << 'EOF'
# Koishi 优化配置模板
# 解决 pixluna 和 QQ 适配器常见问题

host: 0.0.0.0
port: 5140

# 数据库配置
database:
  host: localhost
  port: 3306
  user: root
  password: ''
  database: koishi

# 插件配置
plugins:
  # 基础插件
  console:
    open: true
  
  dataview: {}
  
  # QQ 适配器 - 优化配置
  adapter-qq:
    bots:
      - appid: "你的机器人APPID"
        secret: "你的机器人密钥"
        token: "你的机器人令牌"
    
    # 使用正式环境
    sandbox: false
    
    # 网络配置
    timeout: 15000
    retries: 3
    
    # 错误处理
    handleError: true
    
  # pixluna 插件 - 优化配置
  pixluna:
    # 基础配置
    enabled: true
    
    # 禁用可能导致错误的功能
    autoRecall: false
    
    # 网络配置
    timeout: 30000
    retryCount: 3
    retryDelay: 1000
    
    # 错误处理
    ignoreErrors: true
    logErrors: true
    
    # API 配置
    apiConfig:
      baseURL: "https://api.example.com"  # 替换为正确的 API 地址
      headers:
        "User-Agent": "Koishi-Bot/1.0"
    
  # 其他推荐插件
  recall:
    # 撤回插件配置
    enabled: false  # 暂时禁用以避免冲突
    
  help: {}
  
  echo: {}

# 全局配置
delay:
  # 消息发送延迟
  message: 100
  
  # 撤回延迟
  recall: 5000

# 日志配置
logger:
  levels:
    base: 2
    pixluna: 1  # 启用详细日志以便调试
    adapter-qq: 1
EOF
        
        log "SUCCESS" "配置模板已生成: koishi-optimized.yml"
        log "INFO" "请根据实际情况修改配置并重命名为 koishi.yml"
    fi
}

# 显示调试信息
show_debug_info() {
    echo
    log "INFO" "收集调试信息..."
    echo
    
    echo -e "${BLUE}系统信息：${NC}"
    echo "Node.js 版本: $(node --version)"
    echo "npm 版本: $(npm --version)"
    echo "操作系统: $(uname -s)"
    echo
    
    echo -e "${BLUE}Koishi 信息：${NC}"
    if npm list koishi >/dev/null 2>&1; then
        echo "Koishi 版本: $(npm list koishi --depth=0 2>/dev/null | grep koishi | awk '{print $2}')"
    else
        echo "Koishi: 未安装或版本异常"
    fi
    echo
    
    echo -e "${BLUE}相关插件版本：${NC}"
    for plugin in "koishi-plugin-pixluna" "@satorijs/adapter-qq" "koishi-plugin-recall"; do
        if npm list "$plugin" >/dev/null 2>&1; then
            local version=$(npm list "$plugin" --depth=0 2>/dev/null | grep "$plugin" | awk '{print $2}')
            echo "$plugin: $version"
        else
            echo "$plugin: 未安装"
        fi
    done
    echo
    
    echo -e "${BLUE}最近的错误日志：${NC}"
    if [ -f "koishi.log" ]; then
        tail -10 koishi.log | grep -E "(ERROR|WARN|pixluna|recall)"
    else
        echo "未找到日志文件"
    fi
}

# 主函数
main() {
    show_header
    
    log "INFO" "开始诊断 pixluna 插件问题..."
    echo
    
    # 执行检查
    check_koishi_config
    check_plugin_status
    diagnose_network
    check_qq_adapter
    
    # 显示调试信息
    show_debug_info
    
    # 提供解决方案
    provide_solutions
    
    # 执行修复
    auto_fix
    
    # 生成配置
    generate_config_template
    
    echo
    log "SUCCESS" "诊断完成！"
    echo
    echo -e "${GREEN}下一步建议：${NC}"
    echo "1. 根据上述建议调整配置"
    echo "2. 重启 Koishi 服务"
    echo "3. 观察日志输出"
    echo "4. 如问题持续，考虑联系插件作者"
    echo
}

main "$@"
