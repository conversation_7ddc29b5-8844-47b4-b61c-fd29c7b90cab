#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作版QQ机器人
基于最新botpy API
"""

import asyncio
import logging
import traceback
from datetime import datetime

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class WorkingBot(botpy.Client):
    """工作版QQ机器人"""
    
    def __init__(self):
        # 设置事件订阅
        intents = botpy.Intents(
            public_guild_messages=True,  # 公域消息事件
            direct_message=True,  # 私信事件
        )
        
        super().__init__(intents=intents)
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='[%(levelname)s] %(asctime)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('logs/bot.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)

    async def on_ready(self):
        """机器人启动完成事件"""
        self.logger.info(f"机器人 {self.robot.name} 已上线！")
        self.logger.info(f"机器人ID: {self.robot.id}")
        print(f"✅ 机器人 {self.robot.name} 启动成功！")
        print(f"🤖 机器人ID: {self.robot.id}")

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息"""
        try:
            self.logger.info(f"收到@消息: {message.content} (来自: {message.author.username})")
            print(f"📨 收到@消息: {message.content}")
            
            # 提取消息内容
            content = self.extract_content(message.content)
            
            # 处理不同类型的消息
            if content.startswith('/'):
                await self.handle_command(message, content)
            else:
                await self.handle_chat(message, content)
                
        except Exception as e:
            self.logger.error(f"处理@消息时发生错误: {e}")
            self.logger.error(traceback.format_exc())
            await self.send_error_message(message)

    async def on_direct_message_create(self, message: Message):
        """处理私信消息"""
        try:
            self.logger.info(f"收到私信: {message.content} (来自: {message.author.username})")
            print(f"💬 收到私信: {message.content}")
            
            content = message.content.strip()
            
            if content.startswith('/'):
                await self.handle_command(message, content)
            else:
                await self.handle_chat(message, content)
                
        except Exception as e:
            self.logger.error(f"处理私信时发生错误: {e}")
            await self.send_error_message(message)

    def extract_content(self, raw_content: str) -> str:
        """提取消息内容，去除@机器人部分"""
        import re
        # 使用正则表达式去除@机器人的部分
        content = re.sub(r'<@!\d+>\s*', '', raw_content).strip()
        return content

    async def handle_command(self, message: Message, content: str):
        """处理命令"""
        parts = content.split()
        command = parts[0][1:].lower()  # 去除/前缀
        args = parts[1:] if len(parts) > 1 else []
        
        self.logger.info(f"执行命令: {command}, 参数: {args}")
        print(f"⚡ 执行命令: /{command}")
        
        # 命令路由
        if command == "help":
            await self.cmd_help(message)
        elif command == "ping":
            await self.cmd_ping(message)
        elif command == "time":
            await self.cmd_time(message)
        elif command == "hello":
            await self.cmd_hello(message)
        elif command == "info":
            await self.cmd_info(message)
        else:
            await self.send_message(message, f"❓ 未知命令: /{command}\n输入 /help 查看可用命令")

    async def handle_chat(self, message: Message, content: str):
        """处理普通聊天"""
        if not content:
            return
            
        print(f"💭 处理聊天: {content}")
        
        # 简单的关键词回复
        content_lower = content.lower()
        
        if any(word in content_lower for word in ["你好", "hello", "hi", "嗨"]):
            reply = "你好！很高兴见到你！😊\n输入 /help 查看我的功能"
        elif any(word in content_lower for word in ["再见", "bye", "拜拜"]):
            reply = "再见！期待下次聊天！👋"
        elif any(word in content_lower for word in ["谢谢", "thank", "感谢"]):
            reply = "不客气！很高兴能帮到你！😄"
        elif "机器人" in content_lower or "bot" in content_lower:
            reply = "是的，我是QQ机器人！🤖\n我可以聊天和执行命令，输入 /help 了解更多！"
        elif any(word in content_lower for word in ["时间", "几点"]):
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            reply = f"🕐 现在是: {current_time}"
        else:
            reply = "我收到了你的消息！😊\n输入 /help 查看我能做什么～"
        
        await self.send_message(message, reply)

    # 命令处理函数
    async def cmd_help(self, message: Message):
        """帮助命令"""
        help_text = """🤖 QQ机器人帮助信息

📝 可用命令：
/help - 显示此帮助信息
/ping - 测试机器人响应
/time - 获取当前时间
/hello - 打招呼
/info - 查看机器人信息

💬 聊天功能：
直接@我发送消息即可聊天

🎯 关键词：
- 发送"你好"、"时间"等关键词试试看

❓ 如有问题，请联系管理员"""
        
        await self.send_message(message, help_text)

    async def cmd_ping(self, message: Message):
        """Ping命令"""
        await self.send_message(message, "🏓 Pong! 机器人运行正常！")

    async def cmd_time(self, message: Message):
        """时间命令"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        await self.send_message(message, f"🕐 当前时间: {current_time}")

    async def cmd_hello(self, message: Message):
        """打招呼命令"""
        username = message.author.username if message.author else "朋友"
        await self.send_message(message, f"👋 你好 {username}！我是QQ机器人，很高兴认识你！")

    async def cmd_info(self, message: Message):
        """机器人信息"""
        info_text = f"""🤖 机器人信息

🏷️ 名称: {self.robot.name}
🆔 ID: {self.robot.id}
📱 AppID: {BOT_CONFIG['appid']}
⚡ 状态: 运行中
🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 这是一个基于QQ开放平台的机器人"""
        
        await self.send_message(message, info_text)

    async def send_message(self, original_message: Message, content: str):
        """发送消息"""
        try:
            # 发送消息到频道
            await self.api.post_message(
                channel_id=original_message.channel_id,
                content=content,
                msg_id=original_message.id
            )
            self.logger.info(f"发送消息成功: {content[:50]}...")
            print(f"✅ 消息发送成功")
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            print(f"❌ 发送消息失败: {e}")

    async def send_error_message(self, message: Message):
        """发送错误消息"""
        await self.send_message(message, "❌ 处理消息时发生错误，请稍后重试")


def main():
    """主函数"""
    print("🚀 正在启动QQ机器人...")
    print("=" * 50)
    
    # 检查配置
    if not BOT_CONFIG["appid"]:
        print("❌ AppID未配置")
        return
        
    if not BOT_CONFIG["token"]:
        print("❌ Token未配置")
        return
    
    print(f"📱 AppID: {BOT_CONFIG['appid']}")
    print(f"🔑 Token: {BOT_CONFIG['token'][:10]}...")
    print("=" * 50)
    
    # 创建机器人实例
    bot = WorkingBot()
    
    try:
        # 使用token启动机器人（新版API）
        print("🔄 尝试使用token启动...")
        bot.run(appid=BOT_CONFIG["appid"], token=BOT_CONFIG["token"])
        
    except Exception as e:
        print(f"❌ 使用token启动失败: {e}")
        
        # 尝试使用secret启动（旧版API）
        print("🔄 尝试使用secret启动...")
        try:
            bot.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
        except Exception as e2:
            print(f"❌ 使用secret启动也失败: {e2}")
            
            # 打印详细错误信息
            print("\n📋 详细错误信息:")
            traceback.print_exc()


if __name__ == "__main__":
    main()
