#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ机器人配置测试脚本
用于验证配置是否正确
"""

import sys
import os

def test_config():
    """测试配置文件"""
    try:
        from config import BOT_CONFIG, LOG_CONFIG, FEATURES
        
        print("🔍 正在检查配置...")
        print("=" * 50)
        
        # 检查基本配置
        required_fields = ["appid", "token", "secret"]
        all_configured = True
        
        for field in required_fields:
            value = BOT_CONFIG.get(field, "")
            if not value or value.startswith("你的"):
                print(f"❌ {field}: 未配置")
                all_configured = False
            else:
                # 隐藏敏感信息
                if field == "token":
                    display_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                elif field == "secret":
                    display_value = "***"
                else:
                    display_value = value
                print(f"✅ {field}: {display_value}")
        
        print("=" * 50)
        
        # 检查日志配置
        print("📝 日志配置:")
        print(f"   级别: {LOG_CONFIG.get('level', 'INFO')}")
        print(f"   文件日志: {'启用' if LOG_CONFIG.get('file_enabled', True) else '禁用'}")
        
        # 检查功能配置
        print("🎯 功能配置:")
        print(f"   自动回复: {'启用' if FEATURES.get('auto_reply', True) else '禁用'}")
        print(f"   命令前缀: {FEATURES.get('command_prefix', '/')}")
        
        print("=" * 50)
        
        if all_configured:
            print("✅ 配置检查通过！机器人可以启动")
            return True
        else:
            print("❌ 配置检查失败！请完善配置后重试")
            return False
            
    except ImportError as e:
        print(f"❌ 导入配置文件失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置检查出错: {e}")
        return False

def test_dependencies():
    """测试依赖是否安装"""
    print("\n🔍 检查依赖...")
    print("=" * 50)
    
    dependencies = [
        ("botpy", "qq-botpy"),
        ("aiohttp", "aiohttp"),
        ("asyncio", "内置模块"),
    ]
    
    all_installed = True
    
    for module, package in dependencies:
        try:
            __import__(module)
            print(f"✅ {module}: 已安装")
        except ImportError:
            print(f"❌ {module}: 未安装 (pip install {package})")
            all_installed = False
    
    print("=" * 50)
    
    if all_installed:
        print("✅ 所有依赖已安装")
        return True
    else:
        print("❌ 部分依赖未安装，请先安装依赖")
        return False

def test_environment():
    """测试运行环境"""
    print("\n🔍 检查运行环境...")
    print("=" * 50)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version >= (3, 8):
        print("✅ Python版本符合要求 (3.8+)")
        version_ok = True
    else:
        print("❌ Python版本过低，需要3.8+")
        version_ok = False
    
    # 检查工作目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查必要文件
    required_files = ["bot.py", "config.py"]
    files_ok = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}: 存在")
        else:
            print(f"❌ {file}: 不存在")
            files_ok = False
    
    # 检查logs目录
    if not os.path.exists("logs"):
        print("📁 创建logs目录...")
        try:
            os.makedirs("logs")
            print("✅ logs目录创建成功")
        except Exception as e:
            print(f"❌ 创建logs目录失败: {e}")
    else:
        print("✅ logs目录存在")
    
    print("=" * 50)
    
    return version_ok and files_ok

def main():
    """主函数"""
    print("🤖 QQ机器人配置测试")
    print("=" * 50)
    
    # 测试环境
    env_ok = test_environment()
    
    # 测试依赖
    deps_ok = test_dependencies()
    
    # 测试配置
    config_ok = test_config()
    
    print("\n📊 测试结果汇总:")
    print("=" * 50)
    print(f"运行环境: {'✅ 通过' if env_ok else '❌ 失败'}")
    print(f"依赖检查: {'✅ 通过' if deps_ok else '❌ 失败'}")
    print(f"配置检查: {'✅ 通过' if config_ok else '❌ 失败'}")
    print("=" * 50)
    
    if env_ok and deps_ok and config_ok:
        print("🎉 所有检查通过！可以启动机器人了")
        print("\n启动命令:")
        print("python bot.py")
        return 0
    else:
        print("❌ 部分检查失败，请根据提示解决问题")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
