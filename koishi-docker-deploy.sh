#!/bin/bash

#==============================================================================
# Koishi Docker 一键部署脚本
# 
# 自动安装 Docker 并部署 Koishi
# 
# 使用方法：
#   chmod +x koishi-docker-deploy.sh
#   ./koishi-docker-deploy.sh
#
# 版本：v1.0.0
#==============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置变量
DEFAULT_PORT=5140
KOISHI_PORT=""
CONTAINER_NAME="koishi-bot"
DATA_DIR="$HOME/koishi-data"

log() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    case $level in
        "INFO")  echo -e "${BLUE}[$timestamp] [INFO] $message${NC}" ;;
        "SUCCESS") echo -e "${GREEN}[$timestamp] [SUCCESS] $message${NC}" ;;
        "WARN")  echo -e "${YELLOW}[$timestamp] [WARN] $message${NC}" ;;
        "ERROR") echo -e "${RED}[$timestamp] [ERROR] $message${NC}" ;;
        "FIX")   echo -e "${YELLOW}[$timestamp] [FIX] $message${NC}" ;;
    esac
}

# 进度条显示函数
show_progress() {
    local current=$1
    local total=$2
    local message=$3
    local width=50

    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))

    printf "\r${BLUE}[进度] ${message}${NC} ["
    printf "%${filled}s" | tr ' ' '█'
    printf "%${empty}s" | tr ' ' '░'
    printf "] %d%% (%d/%d)" $percentage $current $total

    if [ $current -eq $total ]; then
        echo
    fi
}

# 实时执行命令并显示进度
execute_with_progress() {
    local command="$1"
    local message="$2"
    local steps="$3"

    log "FIX" "$message"

    # 创建临时文件存储输出
    local temp_file=$(mktemp)
    local pid_file=$(mktemp)

    # 后台执行命令
    (
        eval "$command" > "$temp_file" 2>&1
        echo $? > "$pid_file"
    ) &

    local cmd_pid=$!
    local step=0

    # 显示进度动画
    while kill -0 $cmd_pid 2>/dev/null; do
        step=$((step + 1))
        if [ $step -gt $steps ]; then
            step=$steps
        fi
        show_progress $step $steps "$message"
        sleep 0.5
    done

    # 等待命令完成
    wait $cmd_pid

    # 显示完成进度
    show_progress $steps $steps "$message"

    # 获取退出码
    local exit_code=$(cat "$pid_file")

    # 清理临时文件
    rm -f "$temp_file" "$pid_file"

    return $exit_code
}

# 带进度的包安装函数
install_packages_with_progress() {
    local packages=("$@")
    local total=${#packages[@]}
    local current=0

    for package in "${packages[@]}"; do
        current=$((current + 1))
        show_progress $current $total "安装 $package"

        apt install -y "$package" >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            echo
            log "WARN" "$package 安装可能有问题，继续执行..."
        fi
        sleep 0.2
    done
    echo
}

show_header() {
    echo
    echo -e "${GREEN}========================================"
    echo "    Koishi Docker 一键部署工具"
    echo "========================================"
    echo -e "${NC}"
}

# 选择端口
select_port() {
    echo
    log "INFO" "配置 Koishi 端口..."
    echo

    while true; do
        echo -e "${BLUE}请选择 Koishi 运行端口：${NC}"
        echo "1) 5140 (默认端口)"
        echo "2) 8080 (常用 Web 端口)"
        echo "3) 3000 (Node.js 常用端口)"
        echo "4) 自定义端口"
        echo
        read -p "请输入选项 (1-4): " choice

        case $choice in
            1)
                KOISHI_PORT=5140
                break
                ;;
            2)
                KOISHI_PORT=8080
                break
                ;;
            3)
                KOISHI_PORT=3000
                break
                ;;
            4)
                while true; do
                    read -p "请输入自定义端口 (1024-65535): " custom_port

                    # 验证端口号
                    if [[ "$custom_port" =~ ^[0-9]+$ ]] && [ "$custom_port" -ge 1024 ] && [ "$custom_port" -le 65535 ]; then
                        # 检查端口是否被占用
                        if netstat -tuln 2>/dev/null | grep -q ":$custom_port "; then
                            log "WARN" "端口 $custom_port 已被占用，请选择其他端口"
                            continue
                        fi
                        KOISHI_PORT=$custom_port
                        break
                    else
                        log "ERROR" "无效的端口号，请输入 1024-65535 之间的数字"
                    fi
                done
                break
                ;;
            *)
                log "ERROR" "无效选项，请输入 1-4"
                ;;
        esac
    done

    # 更新容器名称以包含端口信息
    CONTAINER_NAME="koishi-bot-$KOISHI_PORT"

    log "SUCCESS" "已选择端口: $KOISHI_PORT"
    log "INFO" "容器名称: $CONTAINER_NAME"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log "ERROR" "请使用 root 用户运行此脚本"
        log "INFO" "使用命令: sudo $0"
        exit 1
    fi
}

# 检查系统信息
check_system() {
    log "INFO" "检查系统信息..."
    
    # 检查操作系统
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        log "INFO" "操作系统: $NAME $VERSION"
    else
        log "WARN" "无法检测操作系统版本"
    fi
    
    # 检查架构
    ARCH=$(uname -m)
    log "INFO" "系统架构: $ARCH"
    
    # 检查内存
    MEMORY=$(free -h | awk '/^Mem:/ {print $2}')
    log "INFO" "系统内存: $MEMORY"
}

# 安装 Docker
install_docker() {
    log "INFO" "步骤1：安装 Docker..."

    # 检查 Docker 是否已安装
    if command -v docker >/dev/null 2>&1; then
        log "SUCCESS" "Docker 已安装"
        docker --version
        return 0
    fi

    echo
    log "FIX" "开始安装 Docker..."
    echo

    # 步骤1: 更新包索引
    show_progress 1 7 "更新包索引"
    execute_with_progress "apt update" "更新包索引" 10

    # 步骤2: 安装依赖包
    show_progress 2 7 "安装依赖包"
    local deps=("ca-certificates" "curl" "gnupg" "lsb-release")
    install_packages_with_progress "${deps[@]}"

    # 步骤3: 添加 GPG 密钥
    show_progress 3 7 "添加 Docker GPG 密钥"
    mkdir -p /etc/apt/keyrings >/dev/null 2>&1
    execute_with_progress "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg" "下载 GPG 密钥" 5

    # 步骤4: 设置仓库
    show_progress 4 7 "设置 Docker 仓库"
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

    # 步骤5: 更新包索引
    show_progress 5 7 "更新包索引"
    execute_with_progress "apt update" "更新包索引" 8

    # 步骤6: 安装 Docker Engine
    show_progress 6 7 "安装 Docker Engine"
    local docker_packages=("docker-ce" "docker-ce-cli" "containerd.io" "docker-buildx-plugin" "docker-compose-plugin")
    install_packages_with_progress "${docker_packages[@]}"

    # 步骤7: 启动服务
    show_progress 7 7 "启动 Docker 服务"
    systemctl start docker >/dev/null 2>&1
    systemctl enable docker >/dev/null 2>&1

    echo
    if command -v docker >/dev/null 2>&1 && docker info >/dev/null 2>&1; then
        log "SUCCESS" "Docker 安装完成"
        docker --version
        log "SUCCESS" "Docker 服务已启动并设置为开机自启"
    else
        log "ERROR" "Docker 安装失败"
        return 1
    fi
}

# 创建数据目录
create_data_directory() {
    log "INFO" "步骤2：创建数据目录..."
    
    if [ ! -d "$DATA_DIR" ]; then
        mkdir -p "$DATA_DIR"
        log "SUCCESS" "数据目录已创建: $DATA_DIR"
    else
        log "SUCCESS" "数据目录已存在: $DATA_DIR"
    fi
    
    # 设置权限
    chmod 755 "$DATA_DIR"
}

# 检测现有 Koishi 容器
detect_existing_containers() {
    log "INFO" "检测现有 Koishi 容器..."

    # 检查所有 Koishi 相关容器
    local existing_containers=$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "koishi|5140" | grep -v "NAMES" || true)
    local running_containers=$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "koishi|5140" | grep -v "NAMES" || true)

    # 检查端口占用
    local port_occupied=""
    if netstat -tuln 2>/dev/null | grep -q ":$KOISHI_PORT "; then
        port_occupied="yes"
    fi

    # 检查特定容器名称
    local target_container_exists=""
    local target_container_running=""
    if docker ps -a -q -f name="$CONTAINER_NAME" | grep -q .; then
        target_container_exists="yes"
        if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
            target_container_running="yes"
        fi
    fi

    # 返回检测结果
    echo "$existing_containers|$running_containers|$port_occupied|$target_container_exists|$target_container_running"
}

# 显示现有容器状态
show_container_status() {
    echo
    echo -e "${BLUE}======== 现有容器状态 ========${NC}"

    # 显示所有 Koishi 相关容器
    local all_containers=$(docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}" | grep -E "koishi|5140")
    if [ -n "$all_containers" ]; then
        echo -e "${YELLOW}所有 Koishi 相关容器：${NC}"
        echo "$all_containers"
    else
        echo -e "${GREEN}没有发现 Koishi 相关容器${NC}"
    fi

    echo

    # 显示端口占用情况
    echo -e "${YELLOW}端口 $KOISHI_PORT 占用情况：${NC}"
    local port_info=$(netstat -tuln 2>/dev/null | grep ":$KOISHI_PORT " || echo "端口未被占用")
    echo "$port_info"

    echo

    # 显示目标容器状态
    if docker ps -a -q -f name="$CONTAINER_NAME" | grep -q .; then
        echo -e "${YELLOW}目标容器 ($CONTAINER_NAME) 状态：${NC}"
        docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.CreatedAt}}" -f name="$CONTAINER_NAME"

        echo
        echo -e "${YELLOW}容器日志 (最后10行)：${NC}"
        docker logs --tail 10 "$CONTAINER_NAME" 2>/dev/null || echo "无法获取日志"
    else
        echo -e "${GREEN}目标容器 ($CONTAINER_NAME) 不存在${NC}"
    fi

    echo -e "${BLUE}================================${NC}"
    echo
}

# 重启现有容器
restart_existing_container() {
    log "INFO" "重启现有容器..."
    echo

    # 步骤1: 停止容器
    show_progress 1 4 "停止容器"
    docker stop "$CONTAINER_NAME" >/dev/null 2>&1
    log "SUCCESS" "容器已停止"

    # 步骤2: 启动容器
    show_progress 2 4 "启动容器"
    docker start "$CONTAINER_NAME" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log "SUCCESS" "容器已启动"
    else
        log "ERROR" "容器启动失败"
        return 1
    fi

    # 步骤3: 等待服务就绪
    show_progress 3 4 "等待服务就绪"
    for i in {1..10}; do
        show_progress $i 10 "等待服务就绪"
        sleep 1
    done
    echo

    # 步骤4: 验证访问
    show_progress 4 4 "验证服务访问"
    local access_success=0
    for i in {1..3}; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:"$KOISHI_PORT" 2>/dev/null | grep -q "200"; then
            access_success=1
            break
        fi
        sleep 2
    done

    echo
    if [ $access_success -eq 1 ]; then
        log "SUCCESS" "容器重启成功，服务正常运行！"
    else
        log "WARN" "容器已重启，但服务可能仍在初始化"
    fi

    return 0
}

# 交互式容器管理
interactive_container_management() {
    local detection_result="$1"
    IFS='|' read -r existing_containers running_containers port_occupied target_exists target_running <<< "$detection_result"

    # 如果没有发现任何相关容器或端口占用，直接返回继续部署
    if [ -z "$existing_containers" ] && [ "$port_occupied" != "yes" ] && [ "$target_exists" != "yes" ]; then
        log "SUCCESS" "没有发现冲突，继续部署..."
        return 0
    fi

    echo
    log "WARN" "检测到现有的 Koishi 容器或端口占用！"

    while true; do
        echo
        echo -e "${YELLOW}请选择操作：${NC}"
        echo "1) 查看现有容器状态和日志"
        echo "2) 重启现有容器 (如果存在目标容器)"
        echo "3) 停止并重新部署新容器"
        echo "4) 取消操作并退出脚本"
        echo
        read -p "请输入选项 (1-4): " choice

        case $choice in
            1)
                show_container_status
                ;;
            2)
                if [ "$target_exists" = "yes" ]; then
                    if restart_existing_container; then
                        show_deployment_info
                        exit 0
                    else
                        log "ERROR" "重启失败，请选择其他操作"
                    fi
                else
                    log "ERROR" "目标容器 ($CONTAINER_NAME) 不存在，无法重启"
                    log "INFO" "请选择其他操作或重新部署"
                fi
                ;;
            3)
                log "INFO" "将停止现有容器并重新部署..."
                # 停止所有相关容器
                if [ -n "$running_containers" ]; then
                    log "FIX" "停止所有运行中的 Koishi 容器..."
                    docker ps -q --filter "name=koishi" | xargs -r docker stop >/dev/null 2>&1
                    docker ps -a -q --filter "name=koishi" | xargs -r docker rm >/dev/null 2>&1
                fi

                # 如果端口被占用，尝试释放
                if [ "$port_occupied" = "yes" ]; then
                    log "FIX" "尝试释放端口 $KOISHI_PORT..."
                    local pid=$(netstat -tuln 2>/dev/null | grep ":$KOISHI_PORT " | awk '{print $7}' | cut -d'/' -f1 | head -1)
                    if [ -n "$pid" ] && [ "$pid" != "-" ]; then
                        kill -9 "$pid" 2>/dev/null || true
                    fi
                fi

                log "SUCCESS" "清理完成，继续部署..."
                return 0
                ;;
            4)
                log "INFO" "用户取消操作，退出脚本"
                exit 0
                ;;
            *)
                log "ERROR" "无效选项，请输入 1-4"
                ;;
        esac
    done
}

# 拉取并运行 Koishi
deploy_koishi() {
    log "INFO" "步骤4：部署 Koishi..."
    echo

    # 步骤1: 拉取镜像
    show_progress 1 3 "拉取 Koishi 镜像"

    # 创建临时文件监控下载进度
    local temp_log=$(mktemp)

    # 后台拉取镜像
    (
        docker pull koishijs/koishi:latest > "$temp_log" 2>&1
        echo $? > "${temp_log}.exit"
    ) &

    local pull_pid=$!
    local progress_step=0

    # 显示拉取进度
    while kill -0 $pull_pid 2>/dev/null; do
        progress_step=$((progress_step + 1))
        if [ $progress_step -gt 20 ]; then
            progress_step=20
        fi
        show_progress $progress_step 20 "拉取 Koishi 镜像"
        sleep 1
    done

    wait $pull_pid
    show_progress 20 20 "拉取 Koishi 镜像"
    echo

    # 检查拉取结果
    local exit_code=$(cat "${temp_log}.exit" 2>/dev/null || echo "1")
    if [ "$exit_code" -ne 0 ]; then
        log "ERROR" "镜像拉取失败"
        cat "$temp_log"
        rm -f "$temp_log" "${temp_log}.exit"
        return 1
    fi

    log "SUCCESS" "镜像拉取完成"
    rm -f "$temp_log" "${temp_log}.exit"

    # 步骤2: 创建容器
    show_progress 2 3 "创建 Koishi 容器"

    docker run -d \
        --name "$CONTAINER_NAME" \
        --restart unless-stopped \
        -p "$KOISHI_PORT:5140" \
        -v "$DATA_DIR:/koishi/data" \
        -e TZ=Asia/Shanghai \
        koishijs/koishi:latest >/dev/null 2>&1

    if [ $? -eq 0 ]; then
        show_progress 3 3 "启动 Koishi 容器"
        echo
        log "SUCCESS" "Koishi 容器启动成功"
        return 0
    else
        echo
        log "ERROR" "Koishi 容器启动失败"
        return 1
    fi
}

# 配置防火墙
configure_firewall() {
    log "INFO" "步骤5：配置防火墙..."
    
    if command -v ufw >/dev/null 2>&1; then
        ufw allow "$KOISHI_PORT" >/dev/null 2>&1
        log "SUCCESS" "UFW 防火墙规则已添加"
    elif command -v iptables >/dev/null 2>&1; then
        iptables -A INPUT -p tcp --dport "$KOISHI_PORT" -j ACCEPT >/dev/null 2>&1
        log "SUCCESS" "iptables 防火墙规则已添加"
    else
        log "WARN" "未找到防火墙工具，请手动配置"
    fi
}

# 验证部署
verify_deployment() {
    log "INFO" "步骤6：验证部署..."
    echo

    # 步骤1: 等待容器启动
    show_progress 1 4 "等待容器启动"
    for i in {1..10}; do
        show_progress $i 10 "等待容器启动"
        sleep 1
    done
    echo

    # 步骤2: 检查容器状态
    show_progress 2 4 "检查容器状态"
    if docker ps | grep -q "$CONTAINER_NAME"; then
        log "SUCCESS" "容器运行正常"
    else
        echo
        log "ERROR" "容器未正常运行"
        log "INFO" "查看容器日志:"
        docker logs "$CONTAINER_NAME"
        return 1
    fi

    # 步骤3: 检查服务健康状态
    show_progress 3 4 "检查服务健康状态"
    local health_check=0
    for i in {1..5}; do
        if docker exec "$CONTAINER_NAME" ps aux | grep -q "node" 2>/dev/null; then
            health_check=1
            break
        fi
        sleep 2
    done

    if [ $health_check -eq 1 ]; then
        log "SUCCESS" "服务进程运行正常"
    else
        log "WARN" "服务进程检查失败，但容器可能仍在初始化"
    fi

    # 步骤4: 测试网络访问
    show_progress 4 4 "测试网络访问"
    echo

    log "INFO" "测试服务访问..."
    local access_success=0

    for i in {1..5}; do
        show_progress $i 5 "尝试访问服务"

        if curl -s -o /dev/null -w "%{http_code}" http://localhost:"$KOISHI_PORT" 2>/dev/null | grep -q "200"; then
            echo
            log "SUCCESS" "Koishi 服务访问正常！"
            access_success=1
            break
        elif [ $i -eq 5 ]; then
            echo
            log "WARN" "访问验证失败，但容器可能仍在初始化"
            log "INFO" "请等待几分钟后手动访问: http://localhost:$KOISHI_PORT"
        else
            sleep 3
        fi
    done

    return 0
}

# 显示部署信息
show_deployment_info() {
    echo
    echo -e "${GREEN}🎉 Koishi Docker 部署完成！${NC}"
    echo
    echo -e "${BLUE}访问信息：${NC}"
    echo "  本地访问: http://localhost:$KOISHI_PORT"
    echo "  外部访问: http://$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP"):$KOISHI_PORT"
    echo
    echo -e "${BLUE}容器管理：${NC}"
    echo "  管理脚本: ./koishi-manage.sh"
    echo "  查看状态: docker ps"
    echo "  查看日志: docker logs $CONTAINER_NAME"
    echo "  停止容器: docker stop $CONTAINER_NAME"
    echo "  启动容器: docker start $CONTAINER_NAME"
    echo "  重启容器: docker restart $CONTAINER_NAME"
    echo "  删除容器: docker rm -f $CONTAINER_NAME"
    echo
    echo -e "${BLUE}数据目录：${NC}"
    echo "  数据位置: $DATA_DIR"
    echo "  配置文件: $DATA_DIR/koishi.yml"
    echo
    echo -e "${YELLOW}注意事项：${NC}"
    echo "• 如果使用云服务器，请确保安全组开放 $KOISHI_PORT 端口"
    echo "• 首次访问可能需要几分钟初始化"
    echo "• 数据持久化存储在 $DATA_DIR 目录"
    echo "• 容器会自动重启，除非手动停止"
    echo
}

# 创建管理脚本
create_management_scripts() {
    log "INFO" "创建管理脚本..."

    # 创建启动脚本
    cat > "$HOME/koishi-start-$KOISHI_PORT.sh" << EOF
#!/bin/bash
echo "启动 Koishi 容器 (端口: $KOISHI_PORT)..."
docker start $CONTAINER_NAME
echo "Koishi 已启动"
echo "访问地址: http://localhost:$KOISHI_PORT"
EOF

    # 创建停止脚本
    cat > "$HOME/koishi-stop-$KOISHI_PORT.sh" << EOF
#!/bin/bash
echo "停止 Koishi 容器 (端口: $KOISHI_PORT)..."
docker stop $CONTAINER_NAME
echo "Koishi 已停止"
EOF

    # 创建重启脚本
    cat > "$HOME/koishi-restart-$KOISHI_PORT.sh" << EOF
#!/bin/bash
echo "重启 Koishi 容器 (端口: $KOISHI_PORT)..."
docker restart $CONTAINER_NAME
echo "Koishi 已重启"
echo "访问地址: http://localhost:$KOISHI_PORT"
EOF

    # 创建日志查看脚本
    cat > "$HOME/koishi-logs-$KOISHI_PORT.sh" << EOF
#!/bin/bash
echo "查看 Koishi 日志 (端口: $KOISHI_PORT)..."
docker logs -f $CONTAINER_NAME
EOF

    # 创建通用管理脚本（兼容旧版本）
    cat > "$HOME/koishi-manage.sh" << EOF
#!/bin/bash
echo "Koishi 容器管理 (端口: $KOISHI_PORT)"
echo "容器名称: $CONTAINER_NAME"
echo
echo "请选择操作："
echo "1) 启动容器"
echo "2) 停止容器"
echo "3) 重启容器"
echo "4) 查看日志"
echo "5) 查看状态"
echo "6) 删除容器"
echo
read -p "请输入选项 (1-6): " choice

case \$choice in
    1)
        echo "启动 Koishi 容器..."
        docker start $CONTAINER_NAME
        echo "访问地址: http://localhost:$KOISHI_PORT"
        ;;
    2)
        echo "停止 Koishi 容器..."
        docker stop $CONTAINER_NAME
        ;;
    3)
        echo "重启 Koishi 容器..."
        docker restart $CONTAINER_NAME
        echo "访问地址: http://localhost:$KOISHI_PORT"
        ;;
    4)
        echo "查看 Koishi 日志..."
        docker logs -f $CONTAINER_NAME
        ;;
    5)
        echo "容器状态："
        docker ps -a | grep $CONTAINER_NAME
        ;;
    6)
        read -p "确认删除容器? (y/N): " confirm
        if [[ \$confirm == [yY] ]]; then
            docker rm -f $CONTAINER_NAME
            echo "容器已删除"
        fi
        ;;
    *)
        echo "无效选项"
        ;;
esac
EOF

    # 设置执行权限
    chmod +x "$HOME/koishi-start-$KOISHI_PORT.sh"
    chmod +x "$HOME/koishi-stop-$KOISHI_PORT.sh"
    chmod +x "$HOME/koishi-restart-$KOISHI_PORT.sh"
    chmod +x "$HOME/koishi-logs-$KOISHI_PORT.sh"
    chmod +x "$HOME/koishi-manage.sh"

    log "SUCCESS" "管理脚本已创建在 $HOME 目录"
    log "INFO" "主管理脚本: ./koishi-manage.sh"
}

# 主函数
main() {
    show_header
    
    log "INFO" "开始 Koishi Docker 一键部署..."
    echo
    
    # 检查权限
    check_root

    # 选择端口
    select_port

    # 检查系统
    check_system
    echo

    # 安装 Docker
    if ! install_docker; then
        log "ERROR" "Docker 安装失败，部署终止"
        exit 1
    fi
    echo

    # 检测现有容器并进行交互式管理
    local detection_result=$(detect_existing_containers)
    interactive_container_management "$detection_result"
    echo

    # 创建数据目录
    create_data_directory
    echo
    
    # 部署 Koishi
    if ! deploy_koishi; then
        log "ERROR" "Koishi 部署失败"
        exit 1
    fi
    echo
    
    # 配置防火墙
    configure_firewall
    echo
    
    # 验证部署
    verify_deployment
    echo
    
    # 创建管理脚本
    create_management_scripts
    echo
    
    # 显示部署信息
    show_deployment_info
    
    log "SUCCESS" "部署完成！"
}

# 执行主函数
main "$@"
