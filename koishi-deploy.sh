#!/bin/bash

#==============================================================================
# Koishi 聊天机器人框架一键部署脚本
# 
# 功能特性：
# - 自动检测和适配主流 Linux 发行版
# - 智能安装 Node.js 18+ 和相关依赖
# - 优化的网络配置和镜像源设置
# - 完整的错误处理和用户交互
# - 可选的 systemd 服务配置
# - 详细的日志记录和调试信息
#
# 支持的系统：
# - Ubuntu 18.04+ / Debian 10+
# - CentOS 7+ / RHEL 7+ / Rocky Linux 8+
# - Fedora 30+ / openSUSE Leap 15+
# - Arch Linux / Manjaro
#
# 系统要求：
# - 最低 1GB RAM，推荐 2GB+
# - 最低 2GB 可用磁盘空间
# - 稳定的网络连接
# - sudo 权限（用于安装系统包）
#
# 使用方法：
#   curl -fsSL https://raw.githubusercontent.com/your-repo/koishi-deploy.sh | bash
#   或者：
#   wget -O koishi-deploy.sh https://raw.githubusercontent.com/your-repo/koishi-deploy.sh
#   chmod +x koishi-deploy.sh
#   ./koishi-deploy.sh
#
# 作者：Koishi 社区
# 版本：v2.0.0
# 更新：2024-08-01
#==============================================================================

set -euo pipefail  # 严格模式：遇到错误立即退出，未定义变量报错，管道错误传播

#==============================================================================
# 全局变量定义
#==============================================================================

# 脚本版本和信息
readonly SCRIPT_VERSION="2.0.0"
readonly SCRIPT_NAME="Koishi 一键部署脚本"
readonly KOISHI_VERSION="latest"
readonly MIN_NODE_VERSION="18"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly NC='\033[0m' # No Color

# 系统信息变量
OS_NAME=""
OS_VERSION=""
OS_ARCH=""
PACKAGE_MANAGER=""
INSTALL_CMD=""
UPDATE_CMD=""

# 用户配置变量
PROJECT_NAME="koishi-bot"
PROJECT_PATH=""
INSTALL_YARN=true
CONFIGURE_FIREWALL=true
CREATE_SERVICE=false
USE_CHINA_MIRROR=false
KOISHI_PORT=5140

# 日志文件
readonly LOG_FILE="/tmp/koishi-deploy-$(date +%Y%m%d_%H%M%S).log"

#==============================================================================
# 工具函数
#==============================================================================

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message" | tee -a "$LOG_FILE"
            ;;
        "DEBUG")
            echo -e "${PURPLE}[DEBUG]${NC} $message" >> "$LOG_FILE"
            ;;
        *)
            echo -e "$message" | tee -a "$LOG_FILE"
            ;;
    esac
}

# 显示进度条
show_progress() {
    local current=$1
    local total=$2
    local message="$3"
    local percent=$((current * 100 / total))
    local filled=$((percent / 2))
    local empty=$((50 - filled))
    
    printf "\r${CYAN}[%3d%%]${NC} [" "$percent"
    printf "%*s" "$filled" | tr ' ' '='
    printf "%*s" "$empty" | tr ' ' '-'
    printf "] %s" "$message"
    
    if [ "$current" -eq "$total" ]; then
        echo
    fi
}

# 用户确认函数
confirm() {
    local message="$1"
    local default="${2:-n}"
    local prompt
    
    if [ "$default" = "y" ]; then
        prompt="[Y/n]"
    else
        prompt="[y/N]"
    fi
    
    while true; do
        read -p "$(echo -e "${YELLOW}?${NC} $message $prompt: ")" -r response
        
        # 如果用户直接回车，使用默认值
        if [ -z "$response" ]; then
            response="$default"
        fi
        
        case "$response" in
            [Yy]|[Yy][Ee][Ss])
                return 0
                ;;
            [Nn]|[Nn][Oo])
                return 1
                ;;
            *)
                echo -e "${RED}请输入 y/yes 或 n/no${NC}"
                ;;
        esac
    done
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log "WARNING" "检测到 root 用户运行脚本"
        log "WARNING" "为了安全考虑，建议使用普通用户运行此脚本"
        log "WARNING" "脚本会在需要时自动请求 sudo 权限"
        
        if ! confirm "是否继续使用 root 用户运行？" "n"; then
            log "INFO" "脚本已取消执行"
            exit 0
        fi
    fi
}

# 检查系统要求
check_system_requirements() {
    log "INFO" "检查系统要求..."
    
    # 检查内存
    local mem_total=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_gb=$((mem_total / 1024 / 1024))
    
    if [ "$mem_gb" -lt 1 ]; then
        log "WARNING" "系统内存不足 1GB (当前: ${mem_gb}GB)，可能影响运行性能"
        if ! confirm "是否继续安装？" "n"; then
            exit 1
        fi
    else
        log "SUCCESS" "内存检查通过 (${mem_gb}GB)"
    fi
    
    # 检查磁盘空间
    local disk_free=$(df / | awk 'NR==2 {print $4}')
    local disk_gb=$((disk_free / 1024 / 1024))
    
    if [ "$disk_gb" -lt 2 ]; then
        log "ERROR" "磁盘空间不足 2GB (当前可用: ${disk_gb}GB)"
        exit 1
    else
        log "SUCCESS" "磁盘空间检查通过 (可用: ${disk_gb}GB)"
    fi
    
    # 检查网络连接
    if ! ping -c 1 -W 5 8.8.8.8 >/dev/null 2>&1; then
        log "ERROR" "网络连接检查失败，请确保网络连接正常"
        exit 1
    else
        log "SUCCESS" "网络连接检查通过"
    fi
}

#==============================================================================
# 系统检测函数
#==============================================================================

# 检测操作系统
detect_os() {
    log "INFO" "检测操作系统信息..."
    
    # 获取系统架构
    OS_ARCH=$(uname -m)
    log "DEBUG" "系统架构: $OS_ARCH"
    
    # 检查是否支持的架构
    case "$OS_ARCH" in
        x86_64|amd64)
            log "SUCCESS" "支持的系统架构: $OS_ARCH"
            ;;
        aarch64|arm64)
            log "SUCCESS" "支持的系统架构: $OS_ARCH"
            ;;
        *)
            log "ERROR" "不支持的系统架构: $OS_ARCH"
            log "ERROR" "支持的架构: x86_64, amd64, aarch64, arm64"
            exit 1
            ;;
    esac
    
    # 检测发行版
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS_NAME="$ID"
        OS_VERSION="$VERSION_ID"
        
        log "SUCCESS" "检测到操作系统: $PRETTY_NAME"
        log "DEBUG" "OS_NAME: $OS_NAME, OS_VERSION: $OS_VERSION"
        
    elif [ -f /etc/redhat-release ]; then
        if grep -q "CentOS" /etc/redhat-release; then
            OS_NAME="centos"
            OS_VERSION=$(grep -oE '[0-9]+\.[0-9]+' /etc/redhat-release | head -1)
        elif grep -q "Red Hat" /etc/redhat-release; then
            OS_NAME="rhel"
            OS_VERSION=$(grep -oE '[0-9]+\.[0-9]+' /etc/redhat-release | head -1)
        fi
        log "SUCCESS" "检测到操作系统: $(cat /etc/redhat-release)"
        
    elif [ -f /etc/debian_version ]; then
        OS_NAME="debian"
        OS_VERSION=$(cat /etc/debian_version)
        log "SUCCESS" "检测到操作系统: Debian $OS_VERSION"
        
    else
        log "ERROR" "无法检测操作系统类型"
        log "ERROR" "支持的系统: Ubuntu, Debian, CentOS, RHEL, Fedora, openSUSE, Arch Linux"
        exit 1
    fi
    
    # 设置包管理器
    set_package_manager
}

# 设置包管理器
set_package_manager() {
    log "INFO" "配置包管理器..."
    
    case "$OS_NAME" in
        ubuntu|debian)
            PACKAGE_MANAGER="apt"
            UPDATE_CMD="apt update && apt upgrade -y"
            INSTALL_CMD="apt install -y"
            log "SUCCESS" "使用包管理器: APT"
            ;;
        centos|rhel)
            if command_exists dnf; then
                PACKAGE_MANAGER="dnf"
                UPDATE_CMD="dnf update -y"
                INSTALL_CMD="dnf install -y"
                log "SUCCESS" "使用包管理器: DNF"
            else
                PACKAGE_MANAGER="yum"
                UPDATE_CMD="yum update -y"
                INSTALL_CMD="yum install -y"
                log "SUCCESS" "使用包管理器: YUM"
            fi
            ;;
        fedora)
            PACKAGE_MANAGER="dnf"
            UPDATE_CMD="dnf update -y"
            INSTALL_CMD="dnf install -y"
            log "SUCCESS" "使用包管理器: DNF"
            ;;
        opensuse*|sles)
            PACKAGE_MANAGER="zypper"
            UPDATE_CMD="zypper refresh && zypper update -y"
            INSTALL_CMD="zypper install -y"
            log "SUCCESS" "使用包管理器: Zypper"
            ;;
        arch|manjaro)
            PACKAGE_MANAGER="pacman"
            UPDATE_CMD="pacman -Syu --noconfirm"
            INSTALL_CMD="pacman -S --noconfirm"
            log "SUCCESS" "使用包管理器: Pacman"
            ;;
        *)
            log "ERROR" "不支持的操作系统: $OS_NAME"
            log "ERROR" "支持的系统: Ubuntu, Debian, CentOS, RHEL, Fedora, openSUSE, Arch Linux"
            exit 1
            ;;
    esac
}

#==============================================================================
# 用户交互和配置函数
#==============================================================================

# 显示欢迎信息
show_welcome() {
    clear
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🤖 Koishi 聊天机器人框架一键部署脚本                        ║
║                                                                              ║
║  功能特性：                                                                   ║
║  ✅ 自动检测和适配主流 Linux 发行版                                            ║
║  ✅ 智能安装 Node.js 18+ 和相关依赖                                           ║
║  ✅ 优化的网络配置和镜像源设置                                                 ║
║  ✅ 完整的错误处理和用户交互                                                   ║
║  ✅ 可选的 systemd 服务配置                                                   ║
║  ✅ 详细的日志记录和调试信息                                                   ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"

    echo -e "${WHITE}脚本版本：${NC}$SCRIPT_VERSION"
    echo -e "${WHITE}支持系统：${NC}Ubuntu, Debian, CentOS, RHEL, Fedora, openSUSE, Arch Linux"
    echo -e "${WHITE}日志文件：${NC}$LOG_FILE"
    echo

    log "INFO" "开始执行 $SCRIPT_NAME v$SCRIPT_VERSION"
    log "INFO" "日志文件: $LOG_FILE"
}

# 收集用户配置
collect_user_config() {
    log "INFO" "收集用户配置信息..."
    echo

    # 项目名称
    echo -e "${CYAN}📁 项目配置${NC}"
    read -p "$(echo -e "${YELLOW}?${NC} 请输入项目名称 [默认: koishi-bot]: ")" -r input_name
    PROJECT_NAME="${input_name:-koishi-bot}"

    # 验证项目名称
    if [[ ! "$PROJECT_NAME" =~ ^[a-zA-Z0-9_-]+$ ]]; then
        log "ERROR" "项目名称只能包含字母、数字、下划线和连字符"
        exit 1
    fi

    PROJECT_PATH="$HOME/$PROJECT_NAME"
    log "SUCCESS" "项目名称: $PROJECT_NAME"
    log "SUCCESS" "项目路径: $PROJECT_PATH"

    # 检查目录是否存在
    if [ -d "$PROJECT_PATH" ]; then
        log "WARNING" "目录 $PROJECT_PATH 已存在"
        if confirm "是否删除现有目录并重新创建？" "n"; then
            rm -rf "$PROJECT_PATH"
            log "SUCCESS" "已删除现有目录"
        else
            log "ERROR" "安装已取消"
            exit 1
        fi
    fi

    echo

    # 网络配置
    echo -e "${CYAN}🌐 网络配置${NC}"

    # 检测是否在中国
    if curl -s --connect-timeout 3 --max-time 5 "http://ip-api.com/json" | grep -q '"country":"China"' 2>/dev/null; then
        log "INFO" "检测到您在中国，建议使用国内镜像源"
        USE_CHINA_MIRROR=true
    fi

    if confirm "是否使用国内镜像源加速下载？" "$([ "$USE_CHINA_MIRROR" = true ] && echo y || echo n)"; then
        USE_CHINA_MIRROR=true
        log "SUCCESS" "将使用国内镜像源"
    else
        USE_CHINA_MIRROR=false
        log "SUCCESS" "将使用官方源"
    fi

    echo

    # 可选组件
    echo -e "${CYAN}🔧 可选组件${NC}"

    if confirm "是否安装 Yarn 包管理器？" "y"; then
        INSTALL_YARN=true
        log "SUCCESS" "将安装 Yarn"
    else
        INSTALL_YARN=false
        log "SUCCESS" "将仅使用 npm"
    fi

    if confirm "是否配置防火墙规则？" "y"; then
        CONFIGURE_FIREWALL=true
        log "SUCCESS" "将配置防火墙"
    else
        CONFIGURE_FIREWALL=false
        log "SUCCESS" "跳过防火墙配置"
    fi

    if confirm "是否创建 systemd 服务实现开机自启？" "n"; then
        CREATE_SERVICE=true
        log "SUCCESS" "将创建 systemd 服务"
    else
        CREATE_SERVICE=false
        log "SUCCESS" "跳过服务创建"
    fi

    echo

    # 显示配置摘要
    echo -e "${CYAN}📋 配置摘要${NC}"
    echo -e "${WHITE}项目名称：${NC}$PROJECT_NAME"
    echo -e "${WHITE}项目路径：${NC}$PROJECT_PATH"
    echo -e "${WHITE}使用镜像：${NC}$([ "$USE_CHINA_MIRROR" = true ] && echo "国内镜像源" || echo "官方源")"
    echo -e "${WHITE}安装 Yarn：${NC}$([ "$INSTALL_YARN" = true ] && echo "是" || echo "否")"
    echo -e "${WHITE}配置防火墙：${NC}$([ "$CONFIGURE_FIREWALL" = true ] && echo "是" || echo "否")"
    echo -e "${WHITE}创建服务：${NC}$([ "$CREATE_SERVICE" = true ] && echo "是" || echo "否")"
    echo

    if ! confirm "确认以上配置并开始安装？" "y"; then
        log "INFO" "安装已取消"
        exit 0
    fi
}

#==============================================================================
# 系统更新和依赖安装函数
#==============================================================================

# 更新系统
update_system() {
    log "INFO" "更新系统包管理器..."
    show_progress 1 10 "更新包管理器缓存"

    case "$PACKAGE_MANAGER" in
        apt)
            log "DEBUG" "更新 APT 包缓存..."
            if ! sudo apt update 2>&1 | tee -a "$LOG_FILE"; then
                log "WARNING" "更新 APT 缓存失败，尝试修复..."

                # 尝试修复 APT
                sudo apt --fix-broken install -y 2>&1 | tee -a "$LOG_FILE" || true
                sudo dpkg --configure -a 2>&1 | tee -a "$LOG_FILE" || true

                # 再次尝试更新
                if ! sudo apt update 2>&1 | tee -a "$LOG_FILE"; then
                    log "ERROR" "APT 缓存更新失败，请检查网络连接和软件源配置"
                    log "INFO" "您可以尝试手动运行: sudo apt update"
                    exit 1
                fi
            fi

            show_progress 5 10 "升级系统包"
            log "DEBUG" "升级系统包..."
            if ! sudo apt upgrade -y 2>&1 | tee -a "$LOG_FILE"; then
                log "WARNING" "系统包升级失败，继续安装"
                log "INFO" "您可以稍后手动运行: sudo apt upgrade -y"
            fi
            ;;
        yum|dnf)
            show_progress 5 10 "更新系统包"
            log "DEBUG" "更新 $PACKAGE_MANAGER 包..."
            if ! sudo $PACKAGE_MANAGER update -y 2>&1 | tee -a "$LOG_FILE"; then
                log "WARNING" "系统包更新失败，尝试清理缓存..."
                sudo $PACKAGE_MANAGER clean all 2>&1 | tee -a "$LOG_FILE" || true

                if ! sudo $PACKAGE_MANAGER update -y 2>&1 | tee -a "$LOG_FILE"; then
                    log "WARNING" "系统包更新失败，继续安装"
                    log "INFO" "您可以稍后手动运行: sudo $PACKAGE_MANAGER update -y"
                fi
            fi
            ;;
        zypper)
            log "DEBUG" "刷新 Zypper 仓库..."
            if ! sudo zypper refresh 2>&1 | tee -a "$LOG_FILE"; then
                log "WARNING" "更新 Zypper 缓存失败，尝试清理..."
                sudo zypper clean -a 2>&1 | tee -a "$LOG_FILE" || true

                if ! sudo zypper refresh 2>&1 | tee -a "$LOG_FILE"; then
                    log "ERROR" "Zypper 缓存更新失败"
                    exit 1
                fi
            fi

            show_progress 5 10 "升级系统包"
            log "DEBUG" "升级系统包..."
            if ! sudo zypper update -y 2>&1 | tee -a "$LOG_FILE"; then
                log "WARNING" "系统包升级失败，继续安装"
            fi
            ;;
        pacman)
            show_progress 5 10 "更新系统包"
            log "DEBUG" "更新 Pacman 包..."
            if ! sudo pacman -Syu --noconfirm 2>&1 | tee -a "$LOG_FILE"; then
                log "WARNING" "系统包更新失败，尝试清理缓存..."
                sudo pacman -Scc --noconfirm 2>&1 | tee -a "$LOG_FILE" || true

                if ! sudo pacman -Syu --noconfirm 2>&1 | tee -a "$LOG_FILE"; then
                    log "WARNING" "系统包更新失败，继续安装"
                fi
            fi
            ;;
    esac

    show_progress 10 10 "系统更新完成"
    log "SUCCESS" "系统更新完成"
}

# 安装基础依赖
install_dependencies() {
    log "INFO" "安装基础依赖包..."
    show_progress 1 8 "安装基础工具"

    local base_packages=""
    local dev_packages=""

    case "$PACKAGE_MANAGER" in
        apt)
            base_packages="curl wget git unzip ca-certificates gnupg lsb-release"
            dev_packages="build-essential python3 python3-pip make g++"
            ;;
        yum|dnf)
            base_packages="curl wget git unzip ca-certificates gnupg2"
            dev_packages="gcc gcc-c++ make python3 python3-pip"
            if [ "$PACKAGE_MANAGER" = "yum" ]; then
                dev_packages="$dev_packages groupinstall 'Development Tools'"
            else
                dev_packages="$dev_packages @development-tools"
            fi
            ;;
        zypper)
            base_packages="curl wget git unzip ca-certificates gpg2"
            dev_packages="gcc gcc-c++ make python3 python3-pip patterns-devel-base-devel_basis"
            ;;
        pacman)
            base_packages="curl wget git unzip ca-certificates gnupg"
            dev_packages="base-devel python python-pip"
            ;;
    esac

    # 安装基础包 - 增加错误处理和重试机制
    show_progress 3 8 "安装基础工具包"
    log "DEBUG" "尝试安装基础包: $base_packages"

    local retry_count=0
    local max_retries=3

    while [ $retry_count -lt $max_retries ]; do
        # 构建正确的安装命令
        local install_command=""
        case "$PACKAGE_MANAGER" in
            apt)
                install_command="sudo apt install -y $base_packages"
                ;;
            yum)
                install_command="sudo yum install -y $base_packages"
                ;;
            dnf)
                install_command="sudo dnf install -y $base_packages"
                ;;
            zypper)
                install_command="sudo zypper install -y $base_packages"
                ;;
            pacman)
                install_command="sudo pacman -S --noconfirm $base_packages"
                ;;
        esac

        log "DEBUG" "执行命令: $install_command"

        if eval "$install_command" 2>&1 | tee -a "$LOG_FILE"; then
            log "SUCCESS" "基础包安装成功"
            break
        else
            retry_count=$((retry_count + 1))
            log "WARNING" "基础包安装失败，重试 $retry_count/$max_retries"

            if [ $retry_count -lt $max_retries ]; then
                log "INFO" "等待 5 秒后重试..."
                sleep 5

                # 尝试修复包管理器
                case "$PACKAGE_MANAGER" in
                    apt)
                        log "INFO" "尝试修复 APT 包管理器..."
                        sudo apt --fix-broken install -y 2>&1 | tee -a "$LOG_FILE" || true
                        sudo dpkg --configure -a 2>&1 | tee -a "$LOG_FILE" || true
                        ;;
                    yum|dnf)
                        log "INFO" "清理包管理器缓存..."
                        sudo $PACKAGE_MANAGER clean all 2>&1 | tee -a "$LOG_FILE" || true
                        ;;
                esac
            else
                log "ERROR" "基础依赖包安装失败，已重试 $max_retries 次"
                log "ERROR" "请检查网络连接和包管理器状态"
                log "INFO" "您可以尝试手动运行以下命令："
                log "INFO" "$install_command"
                exit 1
            fi
        fi
    done

    # 安装开发工具 - 增加错误处理
    show_progress 6 8 "安装开发工具"
    log "DEBUG" "尝试安装开发工具: $dev_packages"

    if [ "$PACKAGE_MANAGER" = "yum" ] && echo "$dev_packages" | grep -q "groupinstall"; then
        if ! sudo yum groupinstall -y 'Development Tools' 2>&1 | tee -a "$LOG_FILE"; then
            log "WARNING" "安装开发工具组失败，尝试单独安装"
            sudo yum install -y gcc gcc-c++ make python3 python3-pip 2>&1 | tee -a "$LOG_FILE" || true
        fi
    elif [ "$PACKAGE_MANAGER" = "dnf" ] && echo "$dev_packages" | grep -q "@development-tools"; then
        if ! sudo dnf groupinstall -y "@development-tools" 2>&1 | tee -a "$LOG_FILE"; then
            log "WARNING" "安装开发工具组失败，尝试单独安装"
            sudo dnf install -y gcc gcc-c++ make python3 python3-pip 2>&1 | tee -a "$LOG_FILE" || true
        fi
    else
        # 构建正确的开发工具安装命令
        local dev_install_command=""
        case "$PACKAGE_MANAGER" in
            apt)
                dev_install_command="sudo apt install -y $dev_packages"
                ;;
            yum)
                dev_install_command="sudo yum install -y $dev_packages"
                ;;
            dnf)
                dev_install_command="sudo dnf install -y $dev_packages"
                ;;
            zypper)
                dev_install_command="sudo zypper install -y $dev_packages"
                ;;
            pacman)
                dev_install_command="sudo pacman -S --noconfirm $dev_packages"
                ;;
        esac

        log "DEBUG" "执行开发工具安装命令: $dev_install_command"

        if ! eval "$dev_install_command" 2>&1 | tee -a "$LOG_FILE"; then
            log "WARNING" "安装开发工具失败，某些插件可能无法编译"
            log "INFO" "您可以稍后手动安装开发工具："
            log "INFO" "$dev_install_command"
        fi
    fi

    show_progress 8 8 "依赖安装完成"
    log "SUCCESS" "基础依赖安装完成"
}

#==============================================================================
# Node.js 安装函数
#==============================================================================

# 检查 Node.js 版本
check_nodejs_version() {
    if command_exists node; then
        local current_version=$(node --version | sed 's/v//')
        local major_version=$(echo "$current_version" | cut -d. -f1)

        log "INFO" "检测到 Node.js 版本: v$current_version"

        if [ "$major_version" -ge "$MIN_NODE_VERSION" ]; then
            log "SUCCESS" "Node.js 版本符合要求 (>= v$MIN_NODE_VERSION)"
            return 0
        else
            log "WARNING" "Node.js 版本过低 (当前: v$current_version, 要求: >= v$MIN_NODE_VERSION)"
            return 1
        fi
    else
        log "INFO" "未检测到 Node.js"
        return 1
    fi
}

# 安装 Node.js
install_nodejs() {
    log "INFO" "安装 Node.js..."

    # 检查现有版本
    if check_nodejs_version; then
        log "SUCCESS" "Node.js 已安装且版本符合要求，跳过安装"
        return 0
    fi

    show_progress 1 10 "准备安装 Node.js"

    case "$PACKAGE_MANAGER" in
        apt)
            install_nodejs_apt
            ;;
        yum|dnf)
            install_nodejs_rpm
            ;;
        zypper)
            install_nodejs_zypper
            ;;
        pacman)
            install_nodejs_pacman
            ;;
        *)
            install_nodejs_binary
            ;;
    esac

    # 验证安装
    show_progress 9 10 "验证 Node.js 安装"
    if check_nodejs_version; then
        show_progress 10 10 "Node.js 安装完成"
        log "SUCCESS" "Node.js 安装成功"
        log "INFO" "Node.js 版本: $(node --version)"
        log "INFO" "npm 版本: $(npm --version)"
    else
        log "ERROR" "Node.js 安装失败"
        exit 1
    fi
}

# Ubuntu/Debian 安装 Node.js
install_nodejs_apt() {
    show_progress 2 10 "添加 NodeSource 仓库"

    # 添加 NodeSource 仓库
    curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - >/dev/null 2>&1

    show_progress 6 10 "安装 Node.js"
    if ! sudo apt-get install -y nodejs >/dev/null 2>&1; then
        log "ERROR" "通过 APT 安装 Node.js 失败"
        exit 1
    fi
}

# CentOS/RHEL/Fedora 安装 Node.js
install_nodejs_rpm() {
    show_progress 2 10 "添加 NodeSource 仓库"

    # 添加 NodeSource 仓库
    curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash - >/dev/null 2>&1

    show_progress 6 10 "安装 Node.js"
    if ! sudo $PACKAGE_MANAGER install -y nodejs >/dev/null 2>&1; then
        log "ERROR" "通过 $PACKAGE_MANAGER 安装 Node.js 失败"
        exit 1
    fi
}

# openSUSE 安装 Node.js
install_nodejs_zypper() {
    show_progress 2 10 "添加 NodeSource 仓库"

    # 添加 NodeSource 仓库
    curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash - >/dev/null 2>&1

    show_progress 6 10 "安装 Node.js"
    if ! sudo zypper install -y nodejs >/dev/null 2>&1; then
        log "ERROR" "通过 Zypper 安装 Node.js 失败"
        exit 1
    fi
}

# Arch Linux 安装 Node.js
install_nodejs_pacman() {
    show_progress 2 10 "更新包数据库"
    sudo pacman -Sy >/dev/null 2>&1

    show_progress 6 10 "安装 Node.js"
    if ! sudo pacman -S --noconfirm nodejs npm >/dev/null 2>&1; then
        log "ERROR" "通过 Pacman 安装 Node.js 失败"
        exit 1
    fi
}

# 二进制安装 Node.js（通用方法）
install_nodejs_binary() {
    show_progress 2 10 "下载 Node.js 二进制包"

    local node_version="v20.17.0"  # LTS 版本
    local node_arch=""

    case "$OS_ARCH" in
        x86_64|amd64)
            node_arch="x64"
            ;;
        aarch64|arm64)
            node_arch="arm64"
            ;;
        *)
            log "ERROR" "不支持的架构: $OS_ARCH"
            exit 1
            ;;
    esac

    local node_url="https://nodejs.org/dist/$node_version/node-$node_version-linux-$node_arch.tar.xz"
    local temp_dir="/tmp/nodejs-install"

    mkdir -p "$temp_dir"
    cd "$temp_dir"

    if ! curl -fsSL "$node_url" -o "nodejs.tar.xz"; then
        log "ERROR" "下载 Node.js 失败"
        exit 1
    fi

    show_progress 6 10 "安装 Node.js"
    tar -xf nodejs.tar.xz
    sudo cp -r "node-$node_version-linux-$node_arch"/* /usr/local/

    # 创建符号链接
    sudo ln -sf /usr/local/bin/node /usr/bin/node
    sudo ln -sf /usr/local/bin/npm /usr/bin/npm

    cd - >/dev/null
    rm -rf "$temp_dir"
}

# 配置 npm
configure_npm() {
    log "INFO" "配置 npm..."
    show_progress 1 6 "配置 npm 设置"

    # 配置镜像源
    if [ "$USE_CHINA_MIRROR" = true ]; then
        show_progress 2 6 "配置国内镜像源"
        npm config set registry https://registry.npmmirror.com
        log "SUCCESS" "已配置 npm 国内镜像源"
    else
        show_progress 2 6 "使用官方镜像源"
        npm config set registry https://registry.npmjs.org/
        log "SUCCESS" "使用 npm 官方镜像源"
    fi

    # 配置全局安装目录
    show_progress 4 6 "配置全局安装目录"
    local npm_global_dir="$HOME/.npm-global"
    mkdir -p "$npm_global_dir"
    npm config set prefix "$npm_global_dir"

    # 添加到 PATH
    if ! grep -q "$npm_global_dir/bin" "$HOME/.bashrc"; then
        echo "export PATH=$npm_global_dir/bin:\$PATH" >> "$HOME/.bashrc"
        export PATH="$npm_global_dir/bin:$PATH"
        log "SUCCESS" "已添加 npm 全局目录到 PATH"
    fi

    show_progress 6 6 "npm 配置完成"
    log "SUCCESS" "npm 配置完成"
}

# 安装 Yarn
install_yarn() {
    if [ "$INSTALL_YARN" != true ]; then
        log "INFO" "跳过 Yarn 安装"
        return 0
    fi

    log "INFO" "安装 Yarn..."
    show_progress 1 4 "安装 Yarn"

    if command_exists yarn; then
        log "SUCCESS" "Yarn 已安装，版本: $(yarn --version)"
        return 0
    fi

    show_progress 2 4 "下载并安装 Yarn"
    if ! npm install -g yarn >/dev/null 2>&1; then
        log "WARNING" "Yarn 安装失败，将仅使用 npm"
        INSTALL_YARN=false
        return 0
    fi

    show_progress 4 4 "Yarn 安装完成"

    if command_exists yarn; then
        log "SUCCESS" "Yarn 安装成功，版本: $(yarn --version)"

        # 配置 Yarn 镜像源
        if [ "$USE_CHINA_MIRROR" = true ]; then
            yarn config set registry https://registry.npmmirror.com >/dev/null 2>&1
            log "SUCCESS" "已配置 Yarn 国内镜像源"
        fi
    else
        log "WARNING" "Yarn 安装失败，将仅使用 npm"
        INSTALL_YARN=false
    fi
}

#==============================================================================
# Koishi 项目创建和配置函数
#==============================================================================

# 创建 Koishi 项目
create_koishi_project() {
    log "INFO" "创建 Koishi 项目..."
    show_progress 1 12 "准备创建项目"

    # 创建项目目录
    mkdir -p "$PROJECT_PATH"
    cd "$PROJECT_PATH"

    show_progress 2 12 "初始化项目"
    log "INFO" "项目目录: $PROJECT_PATH"

    # 使用 npm 创建项目
    show_progress 3 12 "下载 Koishi 模板"

    # 创建临时的 package.json 来初始化项目
    cat > package.json << EOF
{
  "name": "$PROJECT_NAME",
  "version": "1.0.0",
  "description": "Koishi Bot Project",
  "main": "index.js",
  "scripts": {
    "start": "koishi start",
    "dev": "koishi start --watch"
  },
  "keywords": ["koishi", "bot", "chatbot"],
  "author": "",
  "license": "MIT"
}
EOF

    show_progress 5 12 "安装 Koishi 核心"

    # 安装 Koishi
    local package_manager_cmd="npm"
    local install_cmd="install"

    if [ "$INSTALL_YARN" = true ] && command_exists yarn; then
        package_manager_cmd="yarn"
        install_cmd="add"
    fi

    # 安装核心包
    if ! $package_manager_cmd $install_cmd koishi >/dev/null 2>&1; then
        log "ERROR" "安装 Koishi 核心包失败"
        exit 1
    fi

    show_progress 7 12 "安装基础插件"

    # 安装基础插件
    local basic_plugins=(
        "@koishijs/plugin-console"
        "@koishijs/plugin-dataview"
        "@koishijs/plugin-status"
        "@koishijs/plugin-commands"
        "@koishijs/plugin-help"
        "@koishijs/plugin-rate-limit"
        "@koishijs/plugin-locales"
    )

    for plugin in "${basic_plugins[@]}"; do
        if ! $package_manager_cmd $install_cmd "$plugin" >/dev/null 2>&1; then
            log "WARNING" "安装插件 $plugin 失败"
        fi
    done

    show_progress 9 12 "生成配置文件"

    # 创建基础配置文件
    create_koishi_config

    show_progress 11 12 "设置项目权限"

    # 设置正确的权限
    chown -R "$(whoami):$(whoami)" "$PROJECT_PATH" 2>/dev/null || true

    show_progress 12 12 "项目创建完成"
    log "SUCCESS" "Koishi 项目创建完成"
}

# 创建 Koishi 配置文件
create_koishi_config() {
    log "INFO" "生成 Koishi 配置文件..."

    # 创建 koishi.yml 配置文件
    cat > koishi.yml << EOF
# Koishi 配置文件
# 更多配置选项请参考: https://koishi.chat/zh-CN/manual/usage/

# 服务器配置
host: 0.0.0.0
port: $KOISHI_PORT

# 数据库配置 (SQLite)
database:
  type: sqlite
  path: ./data/koishi.db

# 插件配置
plugins:
  # 控制台插件 (必需)
  console:
    open: false  # 是否自动打开浏览器

  # 数据视图插件
  dataview: {}

  # 状态监控插件
  status: {}

  # 指令管理插件
  commands: {}

  # 帮助插件
  help: {}

  # 频率限制插件
  rate-limit:
    interval: 1000    # 时间间隔 (毫秒)
    quota: 5          # 配额

  # 本地化插件
  locales:
    fallback: zh-CN

# 日志配置
logger:
  levels:
    base: 2         # 基础日志级别
    request: 1      # 请求日志级别

# 延迟配置
delay:
  character: 20     # 每个字符的延迟 (毫秒)
  message: 500      # 每条消息的延迟 (毫秒)

# 请求配置
request:
  timeout: 30000    # 请求超时时间 (毫秒)
  retries: 3        # 重试次数

# 自动重启配置
autoRestart: true

# 监视文件变化 (开发模式)
watch:
  root: .
  ignore:
    - node_modules/**
    - logs/**
    - data/**
EOF

    # 创建数据目录
    mkdir -p data logs

    # 创建 .gitignore 文件
    cat > .gitignore << EOF
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
data/
logs/
*.log

# Environment variables
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Koishi
koishi.lock.yml
EOF

    # 创建启动脚本
    cat > start.sh << 'EOF'
#!/bin/bash

# Koishi 启动脚本

# 检查 Node.js
if ! command -v node >/dev/null 2>&1; then
    echo "错误: 未找到 Node.js"
    exit 1
fi

# 检查项目目录
if [ ! -f "package.json" ]; then
    echo "错误: 当前目录不是有效的 Koishi 项目"
    exit 1
fi

# 启动 Koishi
echo "启动 Koishi..."
npm start
EOF

    chmod +x start.sh

    log "SUCCESS" "配置文件生成完成"
}

#==============================================================================
# 系统配置函数
#==============================================================================

# 配置防火墙
configure_firewall() {
    if [ "$CONFIGURE_FIREWALL" != true ]; then
        log "INFO" "跳过防火墙配置"
        return 0
    fi

    log "INFO" "配置防火墙规则..."
    show_progress 1 6 "检测防火墙类型"

    local firewall_configured=false

    # UFW (Ubuntu/Debian)
    if command_exists ufw; then
        show_progress 3 6 "配置 UFW 防火墙"
        if sudo ufw allow "$KOISHI_PORT/tcp" >/dev/null 2>&1; then
            log "SUCCESS" "UFW 防火墙规则已添加 (端口 $KOISHI_PORT)"
            firewall_configured=true
        else
            log "WARNING" "UFW 防火墙配置失败"
        fi
    fi

    # firewalld (CentOS/RHEL/Fedora)
    if command_exists firewall-cmd && ! $firewall_configured; then
        show_progress 3 6 "配置 firewalld 防火墙"
        if sudo firewall-cmd --permanent --add-port="$KOISHI_PORT/tcp" >/dev/null 2>&1 && \
           sudo firewall-cmd --reload >/dev/null 2>&1; then
            log "SUCCESS" "firewalld 防火墙规则已添加 (端口 $KOISHI_PORT)"
            firewall_configured=true
        else
            log "WARNING" "firewalld 防火墙配置失败"
        fi
    fi

    # iptables (通用)
    if command_exists iptables && ! $firewall_configured; then
        show_progress 3 6 "配置 iptables 防火墙"
        if sudo iptables -A INPUT -p tcp --dport "$KOISHI_PORT" -j ACCEPT >/dev/null 2>&1; then
            log "SUCCESS" "iptables 防火墙规则已添加 (端口 $KOISHI_PORT)"

            # 尝试保存规则
            if command_exists iptables-save; then
                case "$OS_NAME" in
                    ubuntu|debian)
                        sudo iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
                        ;;
                    centos|rhel|fedora)
                        sudo service iptables save >/dev/null 2>&1 || true
                        ;;
                esac
            fi
            firewall_configured=true
        else
            log "WARNING" "iptables 防火墙配置失败"
        fi
    fi

    show_progress 6 6 "防火墙配置完成"

    if ! $firewall_configured; then
        log "WARNING" "未检测到支持的防火墙，请手动开放端口 $KOISHI_PORT"
        log "INFO" "常用防火墙配置命令:"
        log "INFO" "  UFW: sudo ufw allow $KOISHI_PORT/tcp"
        log "INFO" "  firewalld: sudo firewall-cmd --permanent --add-port=$KOISHI_PORT/tcp && sudo firewall-cmd --reload"
        log "INFO" "  iptables: sudo iptables -A INPUT -p tcp --dport $KOISHI_PORT -j ACCEPT"
    else
        log "SUCCESS" "防火墙配置完成"
    fi
}

# 创建 systemd 服务
create_systemd_service() {
    if [ "$CREATE_SERVICE" != true ]; then
        log "INFO" "跳过 systemd 服务创建"
        return 0
    fi

    log "INFO" "创建 systemd 服务..."
    show_progress 1 8 "准备服务配置"

    local service_name="koishi-$PROJECT_NAME"
    local service_file="/etc/systemd/system/$service_name.service"
    local user_name=$(whoami)

    show_progress 3 8 "生成服务文件"

    # 创建服务文件
    sudo tee "$service_file" > /dev/null << EOF
[Unit]
Description=Koishi Bot Service ($PROJECT_NAME)
Documentation=https://koishi.chat/
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=simple
User=$user_name
Group=$user_name
WorkingDirectory=$PROJECT_PATH
ExecStart=/usr/bin/npm start
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
TimeoutStopSec=30
KillMode=mixed

# 环境变量
Environment=NODE_ENV=production
Environment=PATH=/usr/local/bin:/usr/bin:/bin:$HOME/.npm-global/bin

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=read-only
ReadWritePaths=$PROJECT_PATH

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$service_name

[Install]
WantedBy=multi-user.target
EOF

    show_progress 5 8 "重载 systemd 配置"
    sudo systemctl daemon-reload

    show_progress 6 8 "启用服务"
    if sudo systemctl enable "$service_name" >/dev/null 2>&1; then
        log "SUCCESS" "systemd 服务已启用"
    else
        log "ERROR" "启用 systemd 服务失败"
        return 1
    fi

    show_progress 8 8 "服务创建完成"
    log "SUCCESS" "systemd 服务创建完成"
    log "INFO" "服务名称: $service_name"
    log "INFO" "服务管理命令:"
    log "INFO" "  启动服务: sudo systemctl start $service_name"
    log "INFO" "  停止服务: sudo systemctl stop $service_name"
    log "INFO" "  重启服务: sudo systemctl restart $service_name"
    log "INFO" "  查看状态: sudo systemctl status $service_name"
    log "INFO" "  查看日志: sudo journalctl -u $service_name -f"
    log "INFO" "  禁用服务: sudo systemctl disable $service_name"
}

#==============================================================================
# 验证和测试函数
#==============================================================================

# 验证安装
verify_installation() {
    log "INFO" "验证安装结果..."
    show_progress 1 10 "检查项目文件"

    local errors=0

    # 检查项目目录
    if [ ! -d "$PROJECT_PATH" ]; then
        log "ERROR" "项目目录不存在: $PROJECT_PATH"
        ((errors++))
    fi

    show_progress 3 10 "检查配置文件"

    # 检查配置文件
    if [ ! -f "$PROJECT_PATH/koishi.yml" ]; then
        log "ERROR" "配置文件不存在: $PROJECT_PATH/koishi.yml"
        ((errors++))
    fi

    if [ ! -f "$PROJECT_PATH/package.json" ]; then
        log "ERROR" "package.json 不存在"
        ((errors++))
    fi

    show_progress 5 10 "检查依赖安装"

    # 检查 node_modules
    if [ ! -d "$PROJECT_PATH/node_modules" ]; then
        log "ERROR" "依赖包未安装"
        ((errors++))
    fi

    show_progress 7 10 "检查可执行文件"

    # 检查 Node.js
    if ! command_exists node; then
        log "ERROR" "Node.js 未正确安装"
        ((errors++))
    fi

    if ! command_exists npm; then
        log "ERROR" "npm 未正确安装"
        ((errors++))
    fi

    show_progress 9 10 "检查网络端口"

    # 检查端口是否可用
    if command_exists netstat; then
        if netstat -tuln | grep -q ":$KOISHI_PORT "; then
            log "WARNING" "端口 $KOISHI_PORT 已被占用"
        fi
    fi

    show_progress 10 10 "验证完成"

    if [ "$errors" -eq 0 ]; then
        log "SUCCESS" "安装验证通过"
        return 0
    else
        log "ERROR" "发现 $errors 个错误，安装可能不完整"
        return 1
    fi
}

# 测试启动
test_startup() {
    log "INFO" "测试 Koishi 启动..."

    cd "$PROJECT_PATH"

    # 尝试启动 Koishi（超时 30 秒）
    timeout 30s npm start >/dev/null 2>&1 &
    local koishi_pid=$!

    sleep 5

    # 检查进程是否还在运行
    if kill -0 "$koishi_pid" 2>/dev/null; then
        log "SUCCESS" "Koishi 启动测试通过"
        kill "$koishi_pid" 2>/dev/null || true
        wait "$koishi_pid" 2>/dev/null || true
        return 0
    else
        log "ERROR" "Koishi 启动测试失败"
        return 1
    fi
}

#==============================================================================
# 完成和清理函数
#==============================================================================

# 显示完成信息
show_completion() {
    clear
    echo -e "${GREEN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                          🎉 安装完成！                                        ║
║                                                                              ║
║                     Koishi 聊天机器人框架已成功部署                           ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"

    echo -e "${CYAN}📋 安装摘要${NC}"
    echo -e "${WHITE}项目名称：${NC}$PROJECT_NAME"
    echo -e "${WHITE}项目路径：${NC}$PROJECT_PATH"
    echo -e "${WHITE}访问地址：${NC}http://localhost:$KOISHI_PORT"
    echo -e "${WHITE}Node.js 版本：${NC}$(node --version)"
    echo -e "${WHITE}npm 版本：${NC}$(npm --version)"
    if [ "$INSTALL_YARN" = true ] && command_exists yarn; then
        echo -e "${WHITE}Yarn 版本：${NC}$(yarn --version)"
    fi
    echo

    echo -e "${CYAN}🚀 快速开始${NC}"
    echo -e "${WHITE}1. 进入项目目录：${NC}"
    echo -e "   cd $PROJECT_PATH"
    echo
    echo -e "${WHITE}2. 启动 Koishi：${NC}"
    echo -e "   npm start"
    echo -e "   # 或者使用启动脚本"
    echo -e "   ./start.sh"
    echo
    echo -e "${WHITE}3. 访问控制台：${NC}"
    echo -e "   http://localhost:$KOISHI_PORT"
    echo

    if [ "$CREATE_SERVICE" = true ]; then
        echo -e "${CYAN}🔧 系统服务管理${NC}"
        echo -e "${WHITE}启动服务：${NC}sudo systemctl start koishi-$PROJECT_NAME"
        echo -e "${WHITE}停止服务：${NC}sudo systemctl stop koishi-$PROJECT_NAME"
        echo -e "${WHITE}查看状态：${NC}sudo systemctl status koishi-$PROJECT_NAME"
        echo -e "${WHITE}查看日志：${NC}sudo journalctl -u koishi-$PROJECT_NAME -f"
        echo
    fi

    echo -e "${CYAN}📚 更多信息${NC}"
    echo -e "${WHITE}官方文档：${NC}https://koishi.chat/"
    echo -e "${WHITE}插件市场：${NC}https://koishi.chat/market/"
    echo -e "${WHITE}社区支持：${NC}https://koishi.chat/about/contact.html"
    echo -e "${WHITE}GitHub：${NC}https://github.com/koishijs/koishi"
    echo

    echo -e "${CYAN}⚠️  重要提示${NC}"
    echo -e "${WHITE}• 首次启动可能需要几分钟来初始化数据库${NC}"
    echo -e "${WHITE}• 如需外网访问，请确保服务器安全组开放端口 $KOISHI_PORT${NC}"
    echo -e "${WHITE}• 生产环境建议配置反向代理和 HTTPS${NC}"
    echo -e "${WHITE}• 定期备份 data 目录中的数据${NC}"
    echo

    echo -e "${CYAN}📝 日志文件${NC}"
    echo -e "${WHITE}安装日志：${NC}$LOG_FILE"
    echo

    log "SUCCESS" "Koishi 部署完成！"
    log "INFO" "感谢使用 Koishi 一键部署脚本"
}

# 清理函数
cleanup() {
    local exit_code=$?

    if [ $exit_code -ne 0 ]; then
        log "ERROR" "脚本执行过程中发生错误 (退出码: $exit_code)"
        log "INFO" "详细日志请查看: $LOG_FILE"

        echo
        echo -e "${RED}❌ 安装失败${NC}"
        echo -e "${WHITE}错误日志：${NC}$LOG_FILE"
        echo -e "${WHITE}获取帮助：${NC}https://koishi.chat/about/contact.html"
        echo

        # 询问是否保留已创建的文件
        if [ -d "$PROJECT_PATH" ] && [ -n "$PROJECT_PATH" ]; then
            if confirm "是否删除已创建的项目文件？" "n"; then
                rm -rf "$PROJECT_PATH"
                log "INFO" "已删除项目文件: $PROJECT_PATH"
            else
                log "INFO" "保留项目文件: $PROJECT_PATH"
            fi
        fi
    fi

    # 恢复工作目录
    cd "$HOME" 2>/dev/null || true
}

# 信号处理
handle_signal() {
    local signal=$1
    log "WARNING" "收到信号: $signal"
    log "INFO" "正在清理并退出..."
    cleanup
    exit 130
}

#==============================================================================
# 主函数
#==============================================================================

# 主安装流程
main() {
    # 设置信号处理
    trap 'handle_signal SIGINT' INT
    trap 'handle_signal SIGTERM' TERM
    trap 'cleanup' EXIT

    # 显示欢迎信息
    show_welcome

    # 系统检查
    log "INFO" "开始系统检查..."
    check_root
    check_system_requirements
    detect_os

    # 用户配置
    collect_user_config

    # 开始安装
    echo
    log "INFO" "开始安装过程..."

    # 第一阶段：系统准备
    echo -e "${CYAN}📦 第一阶段：系统准备${NC}"
    update_system
    install_dependencies

    # 第二阶段：Node.js 环境
    echo -e "${CYAN}🟢 第二阶段：Node.js 环境${NC}"
    install_nodejs
    configure_npm
    install_yarn

    # 第三阶段：Koishi 部署
    echo -e "${CYAN}🤖 第三阶段：Koishi 部署${NC}"
    create_koishi_project

    # 第四阶段：系统配置
    echo -e "${CYAN}⚙️  第四阶段：系统配置${NC}"
    configure_firewall
    create_systemd_service

    # 第五阶段：验证测试
    echo -e "${CYAN}✅ 第五阶段：验证测试${NC}"
    if ! verify_installation; then
        log "ERROR" "安装验证失败"
        exit 1
    fi

    # 可选：测试启动
    if confirm "是否进行启动测试？" "y"; then
        test_startup
    fi

    # 显示完成信息
    show_completion
}

#==============================================================================
# 脚本入口点
#==============================================================================

# 检查脚本参数
case "${1:-}" in
    --help|-h)
        echo "Koishi 聊天机器人框架一键部署脚本 v$SCRIPT_VERSION"
        echo
        echo "使用方法:"
        echo "  $0                    # 交互式安装"
        echo "  $0 --help            # 显示帮助信息"
        echo "  $0 --version         # 显示版本信息"
        echo
        echo "支持的系统:"
        echo "  • Ubuntu 18.04+"
        echo "  • Debian 10+"
        echo "  • CentOS 7+"
        echo "  • RHEL 7+"
        echo "  • Fedora 30+"
        echo "  • openSUSE Leap 15+"
        echo "  • Arch Linux"
        echo
        echo "系统要求:"
        echo "  • 最低 1GB RAM"
        echo "  • 最低 2GB 可用磁盘空间"
        echo "  • 稳定的网络连接"
        echo "  • sudo 权限"
        echo
        echo "更多信息: https://koishi.chat/"
        exit 0
        ;;
    --version|-v)
        echo "Koishi 部署脚本版本: $SCRIPT_VERSION"
        exit 0
        ;;
    "")
        # 正常执行
        ;;
    *)
        echo "未知参数: $1"
        echo "使用 $0 --help 查看帮助信息"
        exit 1
        ;;
esac

# 检查是否在支持的系统上运行
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "错误: 此脚本仅支持 Linux 系统"
    exit 1
fi

# 检查必要的命令
for cmd in curl wget; do
    if ! command_exists "$cmd"; then
        echo "错误: 缺少必要的命令: $cmd"
        echo "请先安装: sudo apt install $cmd  # Ubuntu/Debian"
        echo "或者:     sudo yum install $cmd  # CentOS/RHEL"
        exit 1
    fi
done

# 创建日志文件
touch "$LOG_FILE" || {
    echo "错误: 无法创建日志文件 $LOG_FILE"
    exit 1
}

# 执行主函数
main "$@"
