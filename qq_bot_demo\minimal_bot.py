#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简QQ机器人
基于官方示例
"""

import botpy
from botpy.types.message import Message

from config import BOT_CONFIG


class MinimalBot(botpy.Client):
    """最简机器人"""
    
    async def on_ready(self):
        """机器人启动完成"""
        print(f"✅ 机器人 {self.robot.name} 启动成功！")

    async def on_at_message_create(self, message: Message):
        """处理@机器人的消息"""
        print(f"📨 收到消息: {message.content}")
        
        # 简单回复
        await self.api.post_message(
            channel_id=message.channel_id, 
            content="你好！我收到了你的消息！",
            msg_id=message.id
        )


def main():
    """主函数"""
    print("🚀 启动最简QQ机器人...")
    
    # 检查配置
    if not BOT_CONFIG["appid"]:
        print("❌ AppID未配置")
        return
        
    if not BOT_CONFIG["secret"]:
        print("❌ Secret未配置")
        return
    
    print(f"📱 AppID: {BOT_CONFIG['appid']}")
    
    # 创建机器人实例
    intents = botpy.Intents(public_guild_messages=True)
    client = MinimalBot(intents=intents)
    
    try:
        # 启动机器人
        client.run(appid=BOT_CONFIG["appid"], secret=BOT_CONFIG["secret"])
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        
        # 尝试其他启动方式
        print("🔄 尝试其他启动方式...")
        try:
            client.run(appid=BOT_CONFIG["appid"], token=BOT_CONFIG["token"])
        except Exception as e2:
            print(f"❌ 第二种方式也失败: {e2}")
            
            # 打印详细错误信息
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
