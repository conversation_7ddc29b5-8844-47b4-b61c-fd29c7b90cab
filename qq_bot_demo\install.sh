#!/bin/bash

# QQ机器人环境安装脚本
# 适用于Ubuntu/Debian系统

echo "🚀 开始安装QQ机器人环境..."

# 更新系统包列表
echo "📦 更新系统包列表..."
sudo apt update

# 安装Python3和pip
echo "🐍 安装Python3和pip..."
sudo apt install -y python3 python3-pip python3-venv

# 检查Python版本
echo "✅ 检查Python版本..."
python3 --version

# 检查pip版本
echo "✅ 检查pip版本..."
pip3 --version

# 创建虚拟环境
echo "🔧 创建Python虚拟环境..."
python3 -m venv qq_bot_env

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source qq_bot_env/bin/activate

# 升级pip
echo "⬆️ 升级pip..."
pip install --upgrade pip

# 安装项目依赖
echo "📚 安装项目依赖..."
pip install qq-botpy aiohttp

# 创建logs目录
echo "📁 创建日志目录..."
mkdir -p logs

echo "✅ 环境安装完成！"
echo ""
echo "🎯 下一步操作："
echo "1. 激活虚拟环境: source qq_bot_env/bin/activate"
echo "2. 运行机器人: python3 bot.py"
echo ""
echo "📝 注意: 请确保已在config.py中配置了正确的机器人信息"
